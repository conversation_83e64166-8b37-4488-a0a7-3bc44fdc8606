# 🗂️ BI系統檔案結構說明

## 📁 清理後的目錄結構

```
BI/
├── 📄 BI系統整合資料匯入工具.py          # 🔧 主要資料匯入工具
├── 📄 BI系統整合資料匯入工具說明.md       # 📖 工具使用說明
├── 📄 執行BI系統資料匯入.bat             # ⚡ 快速執行批次檔
├── 📄 資料來源檔案清單.md               # 📋 資料檔案說明
├── 📄 系統檔案結構說明.md               # 📚 本檔案
├── 📄 app.py                           # 🌐 Flask主應用程式
├── 📄 requirements.txt                 # 📦 Python依賴套件
├── 📄 web.config                       # ⚙️ Web伺服器設定
│
├── 📁 資料來源/                          # 📊 統一資料來源目錄
│   ├── 📄 客戶資料.xlsx                  # 👥 客戶基本資料
│   ├── 📄 2025銷售資料.xlsx              # 💰 當年度銷售資料
│   ├── 📄 維修資料.xlsx                  # 🔧 維修服務資料
│   ├── 📄 寄倉資料.xlsx                  # 📦 寄倉業務資料
│   ├── 📄 昇峰銷售資料.xlsx              # 📈 昇峰歷史銷售資料
│   ├── 📄 昇峰維修資料.xlsx              # 🛠️ 昇峰歷史維修資料
│   ├── 📄 昇峰寄庫資料.xlsx              # 📋 昇峰歷史寄庫資料
│   ├── 📄 服務登記卡.xlsx                # 📝 服務登記卡資料
│   ├── 📄 產品對照資料.xlsx              # 🏷️ 產品QR Code對照表
│   └── 📄 倉別.xlsx                      # 🏪 倉別代碼表
│
├── 📁 database/                         # 🗄️ 主系統資料庫
│   └── 📄 id_database.db                # 👤 使用者帳號資料庫
│
├── 📁 templates/                        # 🎨 網頁模板
│   ├── 📄 base.html                     # 🏗️ 基礎模板
│   ├── 📄 login.html                    # 🔐 登入頁面
│   ├── 📄 dashboard.html                # 🏠 主控台
│   ├── 📄 sales.html                    # 💼 業務查詢頁面
│   ├── 📄 assistant.html                # 🤝 助理查詢頁面
│   ├── 📄 sales_query.html              # 🔍 銷售查詢頁面
│   ├── 📄 inventory_check.html          # 📦 庫存檢查頁面
│   ├── 📄 change_password.html          # 🔑 密碼修改頁面
│   ├── 📄 test_search.html              # 🧪 搜尋測試頁面
│   └── 📄 input_test.html               # ⌨️ 輸入測試頁面
│
├── 📁 Sales_information_inquiry/        # 📊 銷售資訊查詢系統
│   ├── 📄 sales_info.py                 # 🔧 銷售查詢核心程式
│   ├── 📄 README.md                     # 📖 系統說明
│   ├── 📄 requirements.txt              # 📦 系統依賴套件
│   ├── 📁 database/                     # 🗄️ 銷售系統資料庫
│   │   ├── 📄 sales.db                  # 💰 銷售資料庫
│   │   ├── 📄 repair.db                 # 🔧 維修資料庫
│   │   ├── 📄 custody.db                # 📦 寄倉資料庫
│   │   ├── 📄 customer_new.db           # 👥 客戶資料庫
│   │   └── 📄 service_card.db           # 📝 服務登記卡資料庫
│   └── 📁 templates/                    # 🎨 銷售系統模板
│
├── 📁 inventory_system/                 # 📦 庫存盤點系統
│   ├── 📄 inventory.py                  # 🔧 盤點系統核心程式
│   ├── 📄 __init__.py                   # 📦 Python模組初始化
│   ├── 📄 README.md                     # 📖 系統說明
│   ├── 📄 CHANGELOG.md                  # 📝 更新日誌
│   ├── 📁 database/                     # 🗄️ 庫存系統資料庫
│   │   ├── 📄 products.db               # 🏷️ 產品對照資料庫
│   │   ├── 📄 warehouses.db             # 🏪 倉別資料庫
│   │   └── 📄 inventory_records.db      # 📋 盤點記錄資料庫
│   ├── 📁 templates/                    # 🎨 盤點系統模板
│   └── 📁 exports/                      # 📤 盤點結果匯出目錄
│
└── 📁 Inventory_inquiry_system/         # 庫存查詢系統 ⭐ 新增
    ├── 📄 inventory_inquiry.py          # 🔧 庫存查詢核心程式
    ├── 📄 README.md                     # 📖 系統說明
    ├── 📁 templates/                    # 🎨 查詢系統模板
    │   └── 📄 inventory_inquiry.html    # 📋 庫存查詢頁面
    ├── 📁 static/                       # 🎨 靜態資源
    │   ├── 📁 css/                      # 🎨 樣式檔案
    │   └── 📁 js/                       # ⚙️ JavaScript檔案
    └── 📁 database/                     # 🗄️ 庫存查詢資料庫
        └── 📄 inventory_data.db         # 📋 正航庫存資料庫
```

## 🗑️ 已移除的檔案

### 舊的資料匯入工具
- ❌ `Sales_information_inquiry/完整匯入.py` - 已被新工具取代
- ❌ `Sales_information_inquiry/整合工具說明.md` - 已被新說明取代
- ❌ `inventory_system/data_converter.py` - 已整合到新工具
- ❌ `inventory_system/run_data_conversion.bat` - 已被新批次檔取代

### 測試和開發工具
- ❌ `inventory_system/check_excel_structure.py` - 開發測試工具
- ❌ `inventory_system/create_test_data.py` - 測試資料產生器
- ❌ `inventory_system/test_product_search.py` - 產品搜尋測試
- ❌ `inventory_system/fix_database.py` - 資料庫修復工具

### 重複的資料檔案
- ❌ `inventory_system/倉別.xlsx` - 已移至統一資料來源
- ❌ `inventory_system/產品對照資料.xlsx` - 已移至統一資料來源

### 不再使用的工具
- ❌ `inventory_system/條碼產生器.html` - HTML工具
- ❌ `Sales_information_inquiry/服務登記卡轉入.py` - 單獨轉入工具
- ❌ `Sales_information_inquiry/服務登記卡功能說明.md` - 單獨功能說明

### 舊工具和說明檔案
- ❌ `Sales_information_inquiry/舊工具/` - 整個舊工具目錄
- ❌ `客戶資料整合說明.md` - 已整合到新說明
- ❌ `搜尋結果優化說明.md` - 已整合到新說明
- ❌ `搜尋結果篩選功能說明.md` - 已整合到新說明
- ❌ `表頭篩選功能說明.md` - 已整合到新說明

### 空目錄和快取檔案
- ❌ `Inventory Query System/` - 空目錄
- ❌ `__pycache__/` - Python快取目錄
- ❌ `資料來源/正航庫存資料.xlsx` - 未使用的檔案

## ✅ 保留的核心檔案

### 🔧 主要工具
- `BI系統整合資料匯入工具.py` - 統一資料匯入工具
- `執行BI系統資料匯入.bat` - 快速執行批次檔

### 📖 說明文件
- `BI系統整合資料匯入工具說明.md` - 完整使用說明
- `資料來源檔案清單.md` - 資料檔案說明
- `系統檔案結構說明.md` - 本檔案

### 🌐 Web應用程式
- `app.py` - Flask主應用程式
- `templates/` - 網頁模板目錄
- `web.config` - Web伺服器設定

### 📊 系統模組
- `Sales_information_inquiry/` - 銷售資訊查詢系統
- `inventory_system/` - 庫存盤點系統

### 📁 資料目錄
- `資料來源/` - 統一資料來源目錄
- `database/` - 主系統資料庫
- 各系統的 `database/` 目錄

## 💡 清理效果

- **簡化結構**：移除重複和過時的工具
- **統一管理**：所有資料檔案集中在資料來源目錄
- **減少混亂**：移除不再使用的檔案和目錄
- **提升效率**：保留核心功能，移除冗餘內容
- **便於維護**：清晰的檔案組織結構

---

**清理日期：** 2025-01-23  
**版本：** 1.0
