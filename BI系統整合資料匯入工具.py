#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BI系統整合資料匯入工具
支援所有分支系統的資料庫匯入：銷售資訊查詢系統、庫存盤點系統
"""

import pandas as pd
import sqlite3
import os
from datetime import datetime
import time


class BIIntegratedDataConverter:
    def __init__(self):
        # BI根目錄
        self.bi_root = os.path.dirname(__file__)

        # 統一資料來源目錄
        self.data_source_dir = os.path.join(self.bi_root, "資料來源")

        # 銷售資訊查詢系統路徑
        self.sales_base_dir = os.path.join(self.bi_root, "Sales_information_inquiry", "database")
        self.sales_db_path = os.path.join(self.sales_base_dir, "sales.db")
        self.repair_db_path = os.path.join(self.sales_base_dir, "repair.db")
        self.custody_db_path = os.path.join(self.sales_base_dir, "custody.db")
        self.customer_db_path = os.path.join(self.sales_base_dir, "customer_new.db")
        self.service_card_db_path = os.path.join(self.sales_base_dir, "service_card.db")

        # 庫存盤點系統路徑
        self.inventory_base_dir = os.path.join(self.bi_root, "inventory_system", "database")
        self.products_db_path = os.path.join(self.inventory_base_dir, "products.db")
        self.warehouses_db_path = os.path.join(self.inventory_base_dir, "warehouses.db")
        self.inventory_records_db_path = os.path.join(self.inventory_base_dir, "inventory_records.db")

        # 庫存查詢系統路徑
        self.inventory_inquiry_base_dir = os.path.join(self.bi_root, "Inventory_inquiry_system", "database")
        self.inventory_data_db_path = os.path.join(self.inventory_inquiry_base_dir, "inventory_data.db")

        # 確保所有目錄存在
        for db_dir in [self.data_source_dir, self.sales_base_dir, self.inventory_base_dir, self.inventory_inquiry_base_dir]:
            if not os.path.exists(db_dir):
                os.makedirs(db_dir)
    
    def log_time(self, message):
        """記錄時間和訊息"""
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"[{current_time}] {message}")
    
    def clean_date_string(self, date_str):
        """清理日期字串，移除中文星期幾等干擾字符"""
        if pd.isna(date_str) or date_str == '':
            return date_str

        date_str = str(date_str)

        # 移除中文星期幾：(一), (二), (三), (四), (五), (六), (日)
        import re
        date_str = re.sub(r'\s*\([一二三四五六日]\)\s*', '', date_str)

        # 移除其他可能的干擾字符
        date_str = date_str.strip()

        return date_str

    def convert_datetime_optimized(self, df, date_columns):
        """優化的日期時間轉換，支援多種格式包括 Excel 序列號"""
        for col in date_columns:
            if col in df.columns:
                self.log_time(f"🗓️ 處理日期欄位：{col}")

                # 先檢查原始資料
                original_data = df[col].copy()
                non_null_count = original_data.notna().sum()
                self.log_time(f"   原始資料：{non_null_count}/{len(original_data)} 筆有值")

                if non_null_count == 0:
                    self.log_time(f"   ⚠️ {col} 欄位全部為空，跳過轉換")
                    continue

                try:
                    converted = False

                    # 檢查資料類型
                    if pd.api.types.is_datetime64_any_dtype(original_data):
                        # 如果已經是 datetime 格式，直接轉換為字串
                        df[col] = original_data.dt.strftime("%Y-%m-%d %H:%M:%S")
                        converted_count = df[col].notna().sum()
                        self.log_time(f"   ✅ 已是datetime格式，直接轉換：{converted_count} 筆")
                        converted = True

                    # 方法1：檢查是否為 Excel 序列號（數字格式的日期）
                    elif original_data.dtype in ['int64', 'float64'] or pd.api.types.is_numeric_dtype(original_data):
                        try:
                            # 過濾掉明顯不是日期的數字（太小或太大）
                            numeric_data = pd.to_numeric(original_data, errors='coerce')
                            valid_range = (numeric_data >= 1) & (numeric_data <= 100000)  # Excel 日期範圍

                            if valid_range.any():
                                df[col] = pd.to_datetime(numeric_data, origin='1899-12-30', unit='D', errors='coerce')
                                converted_count = df[col].notna().sum()
                                if converted_count > 0:
                                    self.log_time(f"   ✅ Excel序列號轉換成功：{converted_count} 筆")
                                    df[col] = df[col].dt.strftime("%Y-%m-%d %H:%M:%S")
                                    converted = True
                        except Exception as e:
                            self.log_time(f"   ⚠️ Excel序列號轉換失敗：{e}")

                    # 方法2：處理字串格式的日期
                    if not converted:
                        # 先清理日期字串
                        cleaned_data = original_data.apply(self.clean_date_string)
                        self.log_time(f"   🧹 清理日期字串完成")

                        # 嘗試常見的日期格式（優先處理昇峰銷售資料的格式）
                        formats_to_try = [
                            '%Y/%m/%d',           # 昇峰銷售資料格式：2020/01/16
                            '%Y-%m-%d',           # 標準格式
                            '%Y/%m/%d %H:%M:%S',  # 昇峰銷售資料帶時間
                            '%Y-%m-%d %H:%M:%S',  # 標準格式帶時間
                            '%m/%d/%Y',           # 美式格式
                            '%d/%m/%Y',           # 歐式格式
                            '%Y%m%d'              # 緊湊格式
                        ]

                        for fmt in formats_to_try:
                            try:
                                test_conversion = pd.to_datetime(cleaned_data, format=fmt, errors='coerce')
                                converted_count = test_conversion.notna().sum()
                                if converted_count > 0:
                                    df[col] = test_conversion.dt.strftime("%Y-%m-%d %H:%M:%S")
                                    self.log_time(f"   ✅ 格式 {fmt} 轉換成功：{converted_count} 筆")
                                    converted = True
                                    break
                                else:
                                    # 顯示為什麼這個格式沒有轉換成功
                                    sample_data = cleaned_data.dropna().head(3)
                                    if len(sample_data) > 0:
                                        self.log_time(f"   ⚠️ 格式 {fmt} 無法轉換，樣本數據：{list(sample_data)}")
                            except Exception as e:
                                self.log_time(f"   ⚠️ 格式 {fmt} 轉換異常：{e}")
                                continue

                    # 方法3：使用 pandas 預設轉換
                    if not converted:
                        try:
                            cleaned_data = original_data.apply(self.clean_date_string)
                            test_conversion = pd.to_datetime(cleaned_data, errors='coerce')
                            converted_count = test_conversion.notna().sum()
                            if converted_count > 0:
                                df[col] = test_conversion.dt.strftime("%Y-%m-%d %H:%M:%S")
                                self.log_time(f"   ✅ 預設轉換成功：{converted_count} 筆")
                                converted = True
                        except Exception as e:
                            self.log_time(f"   ⚠️ 預設轉換失敗：{e}")

                    # 最終檢查
                    if converted:
                        final_count = df[col].notna().sum()
                        self.log_time(f"   📊 最終轉換結果：{final_count} 筆有效日期")
                    else:
                        # 如果所有方法都失敗，保持原樣但轉為字串
                        df[col] = original_data.astype(str)
                        self.log_time(f"   ⚠️ 日期轉換失敗，保持原始格式")

                except Exception as e:
                    self.log_time(f"   ❌ {col} 轉換過程發生錯誤：{e}")
                    df[col] = original_data.astype(str)

        return df

    def convert_shengfeng_dates(self, df, date_columns):
        """專門處理昇峰銷售資料的日期轉換"""
        for col in date_columns:
            if col in df.columns:
                self.log_time(f"🗓️ 處理昇峰銷售資料日期欄位：{col}")

                original_data = df[col].copy()
                non_null_count = original_data.notna().sum()
                self.log_time(f"   原始資料：{non_null_count}/{len(original_data)} 筆有值")

                if non_null_count == 0:
                    self.log_time(f"   ⚠️ {col} 欄位全部為空，跳過轉換")
                    continue

                try:
                    # 顯示原始數據樣本
                    sample_data = original_data.dropna().head(5)
                    self.log_time(f"   📅 原始日期樣本：{list(sample_data)}")

                    converted = False

                    # 方法1：直接處理YYYY/MM/DD格式
                    if not converted:
                        try:
                            # 先轉換為字符串並清理
                            str_data = original_data.astype(str).str.strip()

                            # 使用正則表達式匹配YYYY/MM/DD格式
                            import re
                            date_pattern = r'^\d{4}/\d{1,2}/\d{1,2}$'
                            valid_dates = str_data[str_data.str.match(date_pattern, na=False)]

                            if len(valid_dates) > 0:
                                # 轉換YYYY/MM/DD格式
                                converted_dates = pd.to_datetime(valid_dates, format='%Y/%m/%d', errors='coerce')

                                # 更新原始數據
                                df.loc[converted_dates.index, col] = converted_dates.dt.strftime("%Y-%m-%d %H:%M:%S")

                                converted_count = converted_dates.notna().sum()
                                self.log_time(f"   ✅ YYYY/MM/DD格式轉換成功：{converted_count} 筆")
                                converted = True
                        except Exception as e:
                            self.log_time(f"   ⚠️ YYYY/MM/DD格式轉換失敗：{e}")

                    # 方法2：使用pandas的智能轉換
                    if not converted:
                        try:
                            test_conversion = pd.to_datetime(original_data, errors='coerce')
                            converted_count = test_conversion.notna().sum()
                            if converted_count > 0:
                                df[col] = test_conversion.dt.strftime("%Y-%m-%d %H:%M:%S")
                                self.log_time(f"   ✅ 智能轉換成功：{converted_count} 筆")
                                converted = True
                        except Exception as e:
                            self.log_time(f"   ⚠️ 智能轉換失敗：{e}")

                    # 最終檢查
                    if converted:
                        final_count = df[col].notna().sum()
                        self.log_time(f"   📊 最終轉換結果：{final_count} 筆有效日期")

                        # 顯示轉換後的樣本
                        converted_sample = df[col].dropna().head(3)
                        self.log_time(f"   📅 轉換後樣本：{list(converted_sample)}")
                    else:
                        self.log_time(f"   ⚠️ 日期轉換失敗，保持原始格式")
                        df[col] = original_data.astype(str)

                except Exception as e:
                    self.log_time(f"   ❌ {col} 轉換過程發生錯誤：{e}")
                    df[col] = original_data.astype(str)

        return df

    def create_indexes_batch(self, cursor, table_name, index_configs):
        """批量建立索引"""
        for column, index_name in index_configs:
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column})")
            except Exception as e:
                print(f"建立索引 {index_name} 失敗: {e}")

    def insert_data_in_chunks(self, df, conn, table_name, chunk_size=1000):
        """分批插入資料，避免 SQLite 變數限制"""
        total_rows = len(df)
        self.log_time(f"📊 開始分批插入 {table_name}：總共 {total_rows:,} 筆，每批 {chunk_size:,} 筆")

        # 先刪除舊表
        cursor = conn.cursor()
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")

        # 分批插入
        for i in range(0, total_rows, chunk_size):
            chunk = df.iloc[i:i+chunk_size]
            if i == 0:
                # 第一批建立表格
                chunk.to_sql(table_name, conn, if_exists="replace", index=False)
            else:
                # 後續批次追加資料
                chunk.to_sql(table_name, conn, if_exists="append", index=False)

            progress = min(i + chunk_size, total_rows)
            self.log_time(f"📈 {table_name} 進度：{progress:,}/{total_rows:,} ({progress/total_rows*100:.1f}%)")

        self.log_time(f"✅ {table_name} 插入完成")

    def convert_customer_data(self):
        """轉換客戶資料 - 使用客戶資料.xlsx"""
        self.log_time("🔄 開始轉換客戶資料...")

        try:
            # 刪除舊的客戶資料庫
            if os.path.exists(self.customer_db_path):
                os.remove(self.customer_db_path)

            # 讀取客戶資料.xlsx（從統一資料來源目錄）
            customer_xlsx_path = os.path.join(self.data_source_dir, "客戶資料.xlsx")
            if not os.path.exists(customer_xlsx_path):
                self.log_time(f"❌ 找不到檔案：{customer_xlsx_path}")
                return

            # 讀取Excel檔案
            df_customer = pd.read_excel(customer_xlsx_path)
            self.log_time(f"📊 讀取客戶資料：{len(df_customer)} 筆")

            # 建立新的客戶資料庫
            conn = sqlite3.connect(self.customer_db_path)
            cursor = conn.cursor()

            # 建立客戶基本資料表
            cursor.execute('''
                CREATE TABLE customer_basic (
                    客戶代碼 TEXT PRIMARY KEY,
                    客戶名稱 TEXT,
                    聯絡地址 TEXT,
                    郵遞區號 TEXT,
                    聯絡電話 TEXT,
                    聯絡人 TEXT,
                    業務人員名稱 TEXT,
                    銷售分類碼名稱 TEXT,
                    預設銷售通路名稱 TEXT,
                    正航舊編碼 TEXT,
                    舊編碼 TEXT,
                    建立時間 DATETIME DEFAULT CURRENT_TIMESTAMP,
                    更新時間 DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 建立客戶聯絡人表
            cursor.execute('''
                CREATE TABLE customer_contacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    客戶代碼 TEXT,
                    聯絡人姓名 TEXT,
                    聯絡電話 TEXT,
                    聯絡手機 TEXT,
                    電子郵件 TEXT,
                    職稱 TEXT,
                    備註 TEXT,
                    FOREIGN KEY (客戶代碼) REFERENCES customer_basic(客戶代碼)
                )
            ''')

            # 建立客戶送貨地址表
            cursor.execute('''
                CREATE TABLE customer_addresses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    客戶代碼 TEXT,
                    地址名稱 TEXT,
                    送貨地址 TEXT,
                    郵遞區號 TEXT,
                    聯絡人 TEXT,
                    聯絡電話 TEXT,
                    是否預設 INTEGER DEFAULT 0,
                    備註 TEXT,
                    FOREIGN KEY (客戶代碼) REFERENCES customer_basic(客戶代碼)
                )
            ''')

            # 處理客戶資料欄位對應
            column_mapping = {
                '客戶代碼': ['客戶代碼', 'Unnamed: 0'],
                '客戶名稱': ['客戶名稱', 'Unnamed: 3'],
                '聯絡地址': ['聯絡地址', 'Unnamed: 2'],
                '郵遞區號': ['郵遞區號', 'Unnamed: 1'],
                '聯絡電話': ['聯絡電話', 'Unnamed: 10'],
                '聯絡人': ['聯絡人', 'Unnamed: 9'],
                '業務人員名稱': ['業務人員名稱', 'Unnamed: 4'],
                '銷售分類碼名稱': ['銷售分類碼名稱', 'Unnamed: 5'],
                '預設銷售通路名稱': ['預設銷售通路名稱', 'Unnamed: 6'],
                '正航舊編碼': ['正航舊編碼', 'Unnamed: 7'],
                '舊編碼': ['舊編碼', 'Unnamed: 8']
            }

            # 建立標準化的客戶資料 DataFrame
            customer_df = pd.DataFrame()
            for target_col, possible_cols in column_mapping.items():
                for col in possible_cols:
                    if col in df_customer.columns:
                        customer_df[target_col] = df_customer[col].astype(str).replace('nan', '').replace('None', '')
                        break
                if target_col not in customer_df.columns:
                    customer_df[target_col] = ''

            # 過濾掉無效的資料行
            customer_df = customer_df[
                (customer_df['客戶代碼'] != '') &
                (customer_df['客戶代碼'] != '客戶資料') &
                (customer_df['客戶代碼'] != '代碼') &
                (customer_df['客戶代碼'].notna())
            ]

            # 使用分批插入方法
            self.insert_data_in_chunks(customer_df, conn, 'customer_basic', chunk_size=1000)

            # 建立索引
            index_configs = [
                ('客戶名稱', 'idx_customer_name'),
                ('客戶代碼', 'idx_customer_code'),
                ('聯絡電話', 'idx_customer_phone'),
                ('聯絡地址', 'idx_customer_address')
            ]
            self.create_indexes_batch(cursor, 'customer_basic', index_configs)

            conn.commit()
            conn.close()

            self.log_time(f"✅ 客戶資料轉換完成：{len(customer_df)} 筆")

        except Exception as e:
            self.log_time(f"❌ 轉換客戶資料時發生錯誤：{str(e)}")

    def convert_sales_data(self):
        """轉換銷售資料"""
        self.log_time("🔄 開始轉換銷售資料...")

        try:
            # 讀取銷售資料檔案（從統一資料來源目錄）
            df_2025_path = os.path.join(self.data_source_dir, "2025銷售資料.xlsx")
            df_shengfeng_path = os.path.join(self.data_source_dir, "昇峰銷售資料.xlsx")

            df_list = []

            if os.path.exists(df_2025_path):
                df_2025 = pd.read_excel(df_2025_path)
                df_list.append(df_2025)
                self.log_time(f"📊 2025銷售資料：{len(df_2025)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{df_2025_path}")

            if os.path.exists(df_shengfeng_path):
                df_shengfeng = pd.read_excel(df_shengfeng_path)
                df_list.append(df_shengfeng)
                self.log_time(f"📊 昇峰銷售資料：{len(df_shengfeng)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{df_shengfeng_path}")

            if not df_list:
                self.log_time("❌ 沒有找到任何銷售資料檔案")
                return

            # 轉換日期時間
            date_columns = ["發貨日期", "單據日期"]
            for i, df in enumerate(df_list):
                if i == 0:  # 2025銷售資料
                    df_list[i] = self.convert_datetime_optimized(df, date_columns)
                else:  # 昇峰銷售資料
                    df_list[i] = self.convert_shengfeng_dates(df, date_columns)

            # 合併資料
            df_combined = pd.concat(df_list, ignore_index=True)
            self.log_time(f"📊 合併後總筆數：{len(df_combined)}")

            # 建立資料庫連接
            conn = sqlite3.connect(self.sales_db_path)

            # 儲存主檔資料（去重複）
            main_columns = ["單據編號", "發貨日期", "客戶名稱", "送貨地址", "聯絡電話", "備註", "業務人員名稱", "客戶代碼"]
            available_main_columns = [col for col in main_columns if col in df_combined.columns]
            df_main = df_combined[available_main_columns].drop_duplicates(subset=["單據編號"])

            # 使用分批插入方法
            self.insert_data_in_chunks(df_main, conn, "sales_main", chunk_size=1000)

            # 儲存明細資料
            detail_columns = ["單據編號", "產品名稱", "交易數量", "交易價", "含稅金額"]
            available_detail_columns = [col for col in detail_columns if col in df_combined.columns]
            df_detail = df_combined[available_detail_columns]

            # 使用分批插入方法
            self.insert_data_in_chunks(df_detail, conn, "sales_detail", chunk_size=1000)

            # 建立索引
            cursor = conn.cursor()
            sales_indexes = [
                ('單據編號', 'idx_sales_main_doc_no'),
                ('客戶名稱', 'idx_sales_main_customer'),
                ('送貨地址', 'idx_sales_main_address'),
                ('聯絡電話', 'idx_sales_main_phone'),
                ('備註', 'idx_sales_main_remark'),
                ('客戶代碼', 'idx_sales_main_customer_code')
            ]
            self.create_indexes_batch(cursor, 'sales_main', sales_indexes)

            detail_indexes = [('單據編號', 'idx_sales_detail_doc_no')]
            self.create_indexes_batch(cursor, 'sales_detail', detail_indexes)

            conn.commit()
            conn.close()

            self.log_time(f"✅ 銷售資料轉換完成：主檔 {len(df_main)} 筆，明細 {len(df_detail)} 筆")

        except Exception as e:
            self.log_time(f"❌ 轉換銷售資料時發生錯誤：{str(e)}")

    def convert_repair_data(self):
        """轉換維修資料"""
        self.log_time("🔄 開始轉換維修資料...")

        try:
            # 讀取維修資料檔案（從統一資料來源目錄）
            df_repair_path = os.path.join(self.data_source_dir, "維修資料.xlsx")
            df_shengfeng_path = os.path.join(self.data_source_dir, "昇峰維修資料.xlsx")

            df_list = []

            if os.path.exists(df_repair_path):
                df_repair = pd.read_excel(df_repair_path)
                df_list.append(df_repair)
                self.log_time(f"📊 維修資料：{len(df_repair)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{df_repair_path}")

            if os.path.exists(df_shengfeng_path):
                df_shengfeng = pd.read_excel(df_shengfeng_path)
                df_list.append(df_shengfeng)
                self.log_time(f"📊 昇峰維修資料：{len(df_shengfeng)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{df_shengfeng_path}")

            if not df_list:
                self.log_time("❌ 沒有找到任何維修資料檔案")
                return

            # 處理客戶欄位（避免浮點數顯示）
            for df in df_list:
                if "客戶" in df.columns:
                    df["客戶"] = df["客戶"].astype(str).replace("nan", "").replace("None", "")
                    # 去除浮點數的小數部分
                    df["客戶"] = df["客戶"].apply(lambda x: x.split(".")[0] if "." in str(x) else x)

            # 轉換日期時間
            date_columns = ["出勤開始時間", "發貨日期", "單據日期"]
            for i, df in enumerate(df_list):
                df_list[i] = self.convert_datetime_optimized(df, date_columns)

            # 合併資料
            df_combined = pd.concat(df_list, ignore_index=True)
            self.log_time(f"📊 合併後總筆數：{len(df_combined)}")

            # 添加客戶代碼欄位
            if "客戶" in df_combined.columns and "客戶代碼" not in df_combined.columns:
                df_combined["客戶代碼"] = df_combined["客戶"]

            # 建立資料庫連接
            conn = sqlite3.connect(self.repair_db_path)

            # 使用分批插入方法
            self.insert_data_in_chunks(df_combined, conn, "repair_data", chunk_size=1000)

            # 建立索引
            cursor = conn.cursor()
            repair_indexes = [
                ('單據編號', 'idx_repair_doc_no'),
                ('客戶名稱', 'idx_repair_customer'),
                ('服務地址', 'idx_repair_address'),
                ('聯絡電話', 'idx_repair_phone'),
                ('備註', 'idx_repair_remark'),
                ('客戶代碼', 'idx_repair_customer_code')
            ]
            self.create_indexes_batch(cursor, 'repair_data', repair_indexes)

            conn.commit()
            conn.close()

            self.log_time(f"✅ 維修資料轉換完成：{len(df_combined)} 筆")

        except Exception as e:
            self.log_time(f"❌ 轉換維修資料時發生錯誤：{str(e)}")

    def convert_custody_data(self):
        """轉換寄倉資料"""
        self.log_time("🔄 開始轉換寄倉資料...")

        try:
            # 讀取寄倉資料檔案（從統一資料來源目錄）
            custody_xlsx_1 = os.path.join(self.data_source_dir, "寄倉資料.xlsx")
            custody_xlsx_2 = os.path.join(self.data_source_dir, "昇峰寄庫資料.xlsx")

            df_list = []
            if os.path.exists(custody_xlsx_1):
                df1 = pd.read_excel(custody_xlsx_1)
                df_list.append(df1)
                self.log_time(f"📊 寄倉資料：{len(df1)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{custody_xlsx_1}")

            if os.path.exists(custody_xlsx_2):
                df2 = pd.read_excel(custody_xlsx_2)
                df_list.append(df2)
                self.log_time(f"📊 昇峰寄庫資料：{len(df2)} 筆")
            else:
                self.log_time(f"⚠️ 找不到檔案：{custody_xlsx_2}")

            if not df_list:
                self.log_time("❌ 沒有找到任何寄倉資料檔案")
                return

            # 合併資料
            df = pd.concat(df_list, ignore_index=True)
            self.log_time(f"📊 合併後總筆數：{len(df)}")

            # 轉換日期時間
            date_columns = ["單據日期"]
            df = self.convert_datetime_optimized(df, date_columns)

            # 主檔欄位對應
            main_columns_mapping = {
                "單據編號": "單據編號",
                "單據日期": "單據日期",
                "借貨對象": "客戶代碼",
                "借貨對象名稱": "客戶名稱",
                "業務人員名稱": "業務人員名稱",
                "聯絡電話": "聯絡電話",
                "送貨地址": "送貨地址",
                "備註": "備註"
            }

            available_main_columns = {k: v for k, v in main_columns_mapping.items() if k in df.columns}
            df_main = df[list(available_main_columns.keys())].rename(columns=available_main_columns)
            df_main = df_main.drop_duplicates(subset=["單據編號"])

            # 處理客戶代碼格式
            if "客戶代碼" in df_main.columns:
                df_main["客戶代碼"] = df_main["客戶代碼"].astype(str).replace("nan", "").replace("None", "")

            # 明細欄位對應
            detail_columns_mapping = {
                "單據編號": "單據編號",
                "產品名稱": "產品名稱",
                "倉庫確認數量": "交易數量"
            }

            available_detail_columns = {k: v for k, v in detail_columns_mapping.items() if k in df.columns}
            df_detail = df[list(available_detail_columns.keys())].rename(columns=available_detail_columns)

            # 建立資料庫連接
            conn = sqlite3.connect(self.custody_db_path)

            # 使用分批插入方法
            self.insert_data_in_chunks(df_main, conn, "custody_main", chunk_size=1000)
            self.insert_data_in_chunks(df_detail, conn, "custody_detail", chunk_size=1000)

            # 建立索引
            cursor = conn.cursor()
            custody_main_indexes = [
                ('單據編號', 'idx_custody_main_doc_no'),
                ('客戶名稱', 'idx_custody_main_customer'),
                ('送貨地址', 'idx_custody_main_address'),
                ('聯絡電話', 'idx_custody_main_phone'),
                ('備註', 'idx_custody_main_remark')
            ]
            self.create_indexes_batch(cursor, 'custody_main', custody_main_indexes)

            custody_detail_indexes = [('單據編號', 'idx_custody_detail_doc_no')]
            self.create_indexes_batch(cursor, 'custody_detail', custody_detail_indexes)

            conn.commit()
            conn.close()

            self.log_time(f"✅ 寄倉資料轉換完成：主檔 {len(df_main)} 筆，明細 {len(df_detail)} 筆")

        except Exception as e:
            self.log_time(f"❌ 轉換寄倉資料時發生錯誤：{str(e)}")

    def convert_service_card_data(self):
        """轉換服務登記卡資料"""
        self.log_time("🔄 開始轉換服務登記卡資料...")

        try:
            # 讀取服務登記卡檔案（從統一資料來源目錄）
            service_card_path = os.path.join(self.data_source_dir, "服務登記卡.xlsx")

            if not os.path.exists(service_card_path):
                self.log_time(f"⚠️ 找不到檔案：{service_card_path}")
                return

            # 讀取Excel檔案，跳過前兩行（標題行）
            df = pd.read_excel(service_card_path, skiprows=2)
            self.log_time(f"📊 服務登記卡原始資料：{len(df)} 筆")

            # 過濾掉空白行
            df = df.dropna(how='all')
            self.log_time(f"📊 服務登記卡有效資料：{len(df)} 筆")

            # 重新命名欄位（根據實際的Excel結構）
            if len(df.columns) >= 18:
                column_mapping = {
                    df.columns[0]: '服務登記號',
                    df.columns[1]: '核算組織',
                    df.columns[2]: '客戶代碼',
                    df.columns[3]: '客戶名稱',
                    df.columns[4]: '聯絡人',
                    df.columns[5]: '聯絡電話',
                    df.columns[6]: '服務地址',
                    df.columns[7]: '產品型號',
                    df.columns[8]: '產品序號',
                    df.columns[9]: '服務項目',
                    df.columns[10]: '服務人員',
                    df.columns[11]: '登記日期',
                    df.columns[12]: '服務日期',
                    df.columns[13]: '完成日期',
                    df.columns[14]: '服務狀態',
                    df.columns[15]: '備註',
                    df.columns[16]: '保固期限',
                    df.columns[17]: '裝機位置說明'
                }
                df = df.rename(columns=column_mapping)

            # 轉換日期時間
            date_columns = ["登記日期", "服務日期", "完成日期", "保固期限"]
            df = self.convert_datetime_optimized(df, date_columns)

            # 處理客戶代碼欄位（避免浮點數顯示）
            if "客戶代碼" in df.columns:
                df["客戶代碼"] = df["客戶代碼"].astype(str).replace("nan", "").replace("None", "")
                # 去除浮點數的小數部分
                df["客戶代碼"] = df["客戶代碼"].apply(lambda x: x.split(".")[0] if "." in str(x) else x)

            # 建立資料庫連接
            conn = sqlite3.connect(self.service_card_db_path)

            # 使用分批插入方法
            self.insert_data_in_chunks(df, conn, "service_card_data", chunk_size=1000)

            # 建立索引（使用實際的欄位名稱）
            cursor = conn.cursor()
            service_card_indexes = [
                ('服務登記號', 'idx_service_card_no'),
                ('客戶名稱', 'idx_service_card_customer'),
                ('客戶代碼', 'idx_service_card_customer_code'),
                ('服務地址', 'idx_service_card_address'),
                ('聯絡電話', 'idx_service_card_phone'),
                ('服務項目', 'idx_service_card_service'),
                ('服務人員', 'idx_service_card_staff'),
                ('登記日期', 'idx_service_card_reg_date'),
                ('服務日期', 'idx_service_card_service_date'),
                ('產品型號', 'idx_service_card_model'),
                ('產品序號', 'idx_service_card_serial')
            ]
            self.create_indexes_batch(cursor, 'service_card_data', service_card_indexes)

            conn.commit()
            conn.close()

            self.log_time(f"✅ 服務登記卡資料轉換完成：{len(df)} 筆")

        except Exception as e:
            self.log_time(f"❌ 轉換服務登記卡資料時發生錯誤：{str(e)}")

    def convert_inventory_products_data(self):
        """轉換庫存系統產品對照資料"""
        self.log_time("🔄 開始轉換庫存系統產品對照資料...")

        try:
            # 讀取Excel檔案（從統一資料來源目錄）
            excel_path = os.path.join(self.data_source_dir, "產品對照資料.xlsx")
            if not os.path.exists(excel_path):
                self.log_time(f"❌ 找不到檔案：{excel_path}")
                return False

            df = pd.read_excel(excel_path)
            self.log_time(f"📊 讀取到 {len(df)} 筆產品資料")

            # 建立資料庫連接
            conn = sqlite3.connect(self.products_db_path)
            cursor = conn.cursor()

            # 刪除舊表格並重新建立
            cursor.execute('DROP TABLE IF EXISTS products')

            # 建立產品表
            cursor.execute('''
                CREATE TABLE products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    qr_code TEXT,
                    product_name TEXT,
                    product_code TEXT,
                    specification TEXT,
                    unit TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入資料
            for index, row in df.iterrows():
                try:
                    # 安全地獲取欄位值
                    values = []
                    for i in range(5):  # 最多取5個欄位
                        if i < len(row) and pd.notna(row.iloc[i]):
                            values.append(str(row.iloc[i]))
                        else:
                            values.append('')

                    qr_code, product_name, product_code, specification, unit = values

                    # 如果前3個欄位都是空的，跳過這筆資料
                    if not any([qr_code, product_name, product_code]):
                        continue

                    cursor.execute('''
                        INSERT OR REPLACE INTO products
                        (qr_code, product_name, product_code, specification, unit)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (qr_code, product_name, product_code, specification, unit))

                except Exception as e:
                    self.log_time(f"⚠️ 第 {index+1} 筆資料插入失敗：{str(e)}")
                    continue

            # 建立索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_qr_code ON products(qr_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_code ON products(product_code)')

            # 提交並關閉
            conn.commit()

            # 驗證資料
            cursor.execute('SELECT COUNT(*) FROM products')
            count = cursor.fetchone()[0]
            conn.close()

            self.log_time(f"✅ 產品對照資料轉換完成：{count} 筆")
            return True

        except Exception as e:
            self.log_time(f"❌ 轉換產品對照資料時發生錯誤：{str(e)}")
            return False

    def convert_inventory_warehouses_data(self):
        """轉換庫存系統倉別資料"""
        self.log_time("🔄 開始轉換庫存系統倉別資料...")

        try:
            # 讀取Excel檔案（從統一資料來源目錄）
            excel_path = os.path.join(self.data_source_dir, "倉別.xlsx")
            if not os.path.exists(excel_path):
                self.log_time(f"❌ 找不到檔案：{excel_path}")
                return False

            df = pd.read_excel(excel_path)
            self.log_time(f"📊 讀取到 {len(df)} 筆倉別資料")

            # 建立資料庫連接
            conn = sqlite3.connect(self.warehouses_db_path)
            cursor = conn.cursor()

            # 刪除舊表格並重新建立
            cursor.execute('DROP TABLE IF EXISTS warehouses')

            # 建立倉別表
            cursor.execute('''
                CREATE TABLE warehouses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    warehouse_code TEXT UNIQUE,
                    warehouse_name TEXT,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 插入資料
            for index, row in df.iterrows():
                try:
                    # 安全地獲取欄位值
                    values = []
                    for i in range(3):  # 最多取3個欄位
                        if i < len(row) and pd.notna(row.iloc[i]):
                            values.append(str(row.iloc[i]))
                        else:
                            values.append('')

                    warehouse_code, warehouse_name, description = values

                    # 如果倉別代碼為空，跳過這筆資料
                    if not warehouse_code:
                        continue

                    cursor.execute('''
                        INSERT OR REPLACE INTO warehouses
                        (warehouse_code, warehouse_name, description)
                        VALUES (?, ?, ?)
                    ''', (warehouse_code, warehouse_name, description))

                except Exception as e:
                    self.log_time(f"⚠️ 第 {index+1} 筆資料插入失敗：{str(e)}")
                    continue

            # 建立索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_warehouse_code ON warehouses(warehouse_code)')

            # 提交並關閉
            conn.commit()

            # 驗證資料
            cursor.execute('SELECT COUNT(*) FROM warehouses')
            count = cursor.fetchone()[0]
            conn.close()

            self.log_time(f"✅ 倉別資料轉換完成：{count} 筆")
            return True

        except Exception as e:
            self.log_time(f"❌ 轉換倉別資料時發生錯誤：{str(e)}")
            return False

    def initialize_inventory_records_db(self):
        """初始化庫存盤點記錄資料庫"""
        self.log_time("🔄 初始化庫存盤點記錄資料庫...")

        try:
            # 建立資料庫連接
            conn = sqlite3.connect(self.inventory_records_db_path)
            cursor = conn.cursor()

            # 建立盤點記錄表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT,
                    qr_code TEXT,
                    product_name TEXT,
                    warehouse_code TEXT,
                    warehouse_name TEXT,
                    quantity INTEGER,
                    inventory_date DATE,
                    inventory_time DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 建立索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_id ON inventory_records(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_qr_code ON inventory_records(qr_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_records(inventory_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_warehouse_code ON inventory_records(warehouse_code)')

            # 提交並關閉
            conn.commit()
            conn.close()

            self.log_time(f"✅ 庫存盤點記錄資料庫初始化完成")
            return True

        except Exception as e:
            self.log_time(f"❌ 初始化庫存盤點記錄資料庫時發生錯誤：{str(e)}")
            return False

    def convert_inventory_inquiry_data(self):
        """轉換正航庫存資料"""
        self.log_time("🔄 開始轉換正航庫存資料...")

        try:
            # 讀取Excel檔案（從統一資料來源目錄）
            excel_path = os.path.join(self.data_source_dir, "正航庫存資料.xlsx")
            if not os.path.exists(excel_path):
                self.log_time(f"❌ 找不到檔案：{excel_path}")
                return False

            df = pd.read_excel(excel_path)
            self.log_time(f"📊 讀取到 {len(df)} 筆庫存資料")

            # 建立資料庫連接
            conn = sqlite3.connect(self.inventory_data_db_path)
            cursor = conn.cursor()

            # 刪除舊表格並重新建立
            cursor.execute('DROP TABLE IF EXISTS inventory_data')

            # 建立庫存資料表
            cursor.execute('''
                CREATE TABLE inventory_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_name TEXT,
                    warehouse_name TEXT,
                    warehouse_partner_name TEXT,
                    product_code TEXT,
                    specification TEXT,
                    unit TEXT,
                    quantity REAL,
                    unit_price REAL,
                    total_amount REAL,
                    last_update_date TEXT,
                    remark TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 處理資料欄位對應（根據實際Excel結構調整）
            # 先檢查Excel檔案的欄位結構
            self.log_time(f"📋 Excel欄位：{list(df.columns)}")

            # 如果Excel檔案有資料，進行轉換
            if len(df) > 0:
                # 建立欄位對應字典
                column_mapping = {}
                for col in df.columns:
                    if '產品名稱' in col:
                        column_mapping[col] = 'product_name'
                    elif '倉庫名稱' in col:
                        column_mapping[col] = 'warehouse_name'
                    elif '倉庫往來對象名稱' in col or '往來對象' in col:
                        column_mapping[col] = 'warehouse_partner_name'
                    elif '產品代碼' in col or '代碼' in col:
                        column_mapping[col] = 'product_code'
                    elif '規格' in col:
                        column_mapping[col] = 'specification'
                    elif '單位' in col:
                        column_mapping[col] = 'unit'
                    elif '數量' in col:
                        column_mapping[col] = 'quantity'
                    elif '單價' in col:
                        column_mapping[col] = 'unit_price'
                    elif '總金額' in col or '金額' in col:
                        column_mapping[col] = 'total_amount'
                    elif '更新日期' in col or '日期' in col:
                        column_mapping[col] = 'last_update_date'
                    elif '備註' in col:
                        column_mapping[col] = 'remark'

                # 重新命名欄位
                df_mapped = df.rename(columns=column_mapping)

                # 確保必要欄位存在，如果不存在則填入空值
                required_columns = ['product_name', 'warehouse_name', 'warehouse_partner_name',
                                  'product_code', 'specification', 'unit', 'quantity',
                                  'unit_price', 'total_amount', 'last_update_date', 'remark']

                for col in required_columns:
                    if col not in df_mapped.columns:
                        df_mapped[col] = ''

                # 只保留需要的欄位
                df_final = df_mapped[required_columns]

                # 使用分批插入方法
                self.insert_data_in_chunks(df_final, conn, "inventory_data", chunk_size=1000)

                # 建立索引（針對查詢需求）
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_name ON inventory_data(product_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_warehouse_name ON inventory_data(warehouse_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_warehouse_partner_name ON inventory_data(warehouse_partner_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_code ON inventory_data(product_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_quantity ON inventory_data(quantity)')

                # 建立複合索引（提升多欄位查詢效能）
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_warehouse ON inventory_data(product_name, warehouse_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_warehouse_partner ON inventory_data(warehouse_name, warehouse_partner_name)')

            # 提交並關閉
            conn.commit()

            # 驗證資料
            cursor.execute('SELECT COUNT(*) FROM inventory_data')
            count = cursor.fetchone()[0]
            conn.close()

            self.log_time(f"✅ 正航庫存資料轉換完成：{count} 筆")
            return True

        except Exception as e:
            self.log_time(f"❌ 轉換正航庫存資料時發生錯誤：{str(e)}")
            return False

    def verify_data_integrity(self):
        """驗證資料完整性"""
        self.log_time("🔍 驗證資料完整性...")

        # 銷售資訊查詢系統資料庫
        sales_databases = [
            (self.sales_db_path, "sales_main", "銷售主檔"),
            (self.sales_db_path, "sales_detail", "銷售明細"),
            (self.repair_db_path, "repair_data", "維修資料"),
            (self.custody_db_path, "custody_main", "寄倉主檔"),
            (self.custody_db_path, "custody_detail", "寄倉明細"),
            (self.customer_db_path, "customer_basic", "客戶基本資料"),
            (self.service_card_db_path, "service_card_data", "服務登記卡資料")
        ]

        # 庫存盤點系統資料庫
        inventory_databases = [
            (self.products_db_path, "products", "產品對照資料"),
            (self.warehouses_db_path, "warehouses", "倉別資料"),
            (self.inventory_records_db_path, "inventory_records", "盤點記錄")
        ]

        # 庫存查詢系統資料庫
        inventory_inquiry_databases = [
            (self.inventory_data_db_path, "inventory_data", "正航庫存資料")
        ]

        self.log_time("📊 銷售資訊查詢系統：")
        for db_path, table_name, description in sales_databases:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    conn.close()
                    self.log_time(f"   ✅ {description}：{count:,} 筆")
                except Exception as e:
                    self.log_time(f"   ❌ {description} 驗證失敗：{e}")
            else:
                self.log_time(f"   ⚠️ 找不到資料庫：{db_path}")

        self.log_time("📦 庫存盤點系統：")
        for db_path, table_name, description in inventory_databases:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    conn.close()
                    self.log_time(f"   ✅ {description}：{count:,} 筆")
                except Exception as e:
                    self.log_time(f"   ❌ {description} 驗證失敗：{e}")
            else:
                self.log_time(f"   ⚠️ 找不到資料庫：{db_path}")

        self.log_time("📋 庫存查詢系統：")
        for db_path, table_name, description in inventory_inquiry_databases:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    conn.close()
                    self.log_time(f"   ✅ {description}：{count:,} 筆")
                except Exception as e:
                    self.log_time(f"   ❌ {description} 驗證失敗：{e}")
            else:
                self.log_time(f"   ⚠️ 找不到資料庫：{db_path}")

    def run_bi_full_conversion(self):
        """執行BI系統完整的資料轉換流程（包含所有分支系統）"""
        start_time = time.time()
        self.log_time("🚀 開始BI系統整合資料轉換流程...")

        try:
            # 1. 轉換銷售資訊查詢系統資料
            self.log_time("📊 === 銷售資訊查詢系統 ===")
            self.convert_customer_data()
            self.convert_sales_data()
            self.convert_repair_data()
            self.convert_custody_data()
            self.convert_service_card_data()

            # 2. 轉換庫存盤點系統資料
            self.log_time("📦 === 庫存盤點系統 ===")
            self.convert_inventory_products_data()
            self.convert_inventory_warehouses_data()
            self.initialize_inventory_records_db()

            # 3. 轉換庫存查詢系統資料
            self.log_time("📋 === 庫存查詢系統 ===")
            self.convert_inventory_inquiry_data()

            # 4. 驗證資料完整性
            self.verify_data_integrity()

            # 計算總耗時
            end_time = time.time()
            total_time = end_time - start_time
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)

            self.log_time(f"🎉 BI系統資料轉換完成！總耗時：{minutes}分{seconds}秒")

        except Exception as e:
            self.log_time(f"❌ BI系統資料轉換過程發生錯誤：{str(e)}")
            raise

    def run_bi_daily_update(self):
        """BI系統日常更新模式"""
        start_time = time.time()
        self.log_time("📅 開始BI系統日常更新模式...")

        try:
            # 1. 更新銷售資訊查詢系統
            self.log_time("📊 === 更新銷售資訊查詢系統 ===")
            self.convert_customer_data()
            self.convert_sales_data()
            self.convert_repair_data()
            self.convert_custody_data()
            self.convert_service_card_data()

            # 2. 更新庫存盤點系統基礎資料（產品和倉別）
            self.log_time("📦 === 更新庫存盤點系統基礎資料 ===")
            self.convert_inventory_products_data()
            self.convert_inventory_warehouses_data()

            # 3. 更新庫存查詢系統資料
            self.log_time("📋 === 更新庫存查詢系統資料 ===")
            self.convert_inventory_inquiry_data()

            # 4. 驗證更新結果
            self.verify_data_integrity()

            # 計算總耗時
            end_time = time.time()
            total_time = end_time - start_time
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)

            self.log_time(f"📅 BI系統日常更新完成！耗時：{minutes}分{seconds}秒")

        except Exception as e:
            self.log_time(f"❌ BI系統日常更新過程發生錯誤：{str(e)}")
            raise

    def run_inventory_only_conversion(self):
        """僅轉換庫存盤點系統資料"""
        start_time = time.time()
        self.log_time("📦 開始庫存盤點系統資料轉換...")

        try:
            # 轉換庫存盤點系統資料
            self.convert_inventory_products_data()
            self.convert_inventory_warehouses_data()
            self.initialize_inventory_records_db()

            # 驗證結果
            self.log_time("📦 庫存盤點系統：")
            inventory_databases = [
                (self.products_db_path, "products", "產品對照資料"),
                (self.warehouses_db_path, "warehouses", "倉別資料"),
                (self.inventory_records_db_path, "inventory_records", "盤點記錄")
            ]

            for db_path, table_name, description in inventory_databases:
                if os.path.exists(db_path):
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        conn.close()
                        self.log_time(f"   ✅ {description}：{count:,} 筆")
                    except Exception as e:
                        self.log_time(f"   ❌ {description} 驗證失敗：{e}")
                else:
                    self.log_time(f"   ⚠️ 找不到資料庫：{db_path}")

            # 計算總耗時
            end_time = time.time()
            total_time = end_time - start_time
            self.log_time(f"📦 庫存盤點系統轉換完成！耗時：{int(total_time)}秒")

        except Exception as e:
            self.log_time(f"❌ 庫存盤點系統轉換過程發生錯誤：{str(e)}")
            raise

    def run_sales_only_conversion(self):
        """僅轉換銷售資訊查詢系統資料"""
        start_time = time.time()
        self.log_time("📊 開始銷售資訊查詢系統資料轉換...")

        try:
            # 轉換銷售資訊查詢系統資料
            self.convert_customer_data()
            self.convert_sales_data()
            self.convert_repair_data()
            self.convert_custody_data()
            self.convert_service_card_data()

            # 驗證結果
            self.log_time("📊 銷售資訊查詢系統：")
            sales_databases = [
                (self.sales_db_path, "sales_main", "銷售主檔"),
                (self.sales_db_path, "sales_detail", "銷售明細"),
                (self.repair_db_path, "repair_data", "維修資料"),
                (self.custody_db_path, "custody_main", "寄倉主檔"),
                (self.custody_db_path, "custody_detail", "寄倉明細"),
                (self.customer_db_path, "customer_basic", "客戶基本資料"),
                (self.service_card_db_path, "service_card_data", "服務登記卡資料")
            ]

            for db_path, table_name, description in sales_databases:
                if os.path.exists(db_path):
                    try:
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        conn.close()
                        self.log_time(f"   ✅ {description}：{count:,} 筆")
                    except Exception as e:
                        self.log_time(f"   ❌ {description} 驗證失敗：{e}")
                else:
                    self.log_time(f"   ⚠️ 找不到資料庫：{db_path}")

            # 計算總耗時
            end_time = time.time()
            total_time = end_time - start_time
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)
            self.log_time(f"📊 銷售資訊查詢系統轉換完成！耗時：{minutes}分{seconds}秒")

        except Exception as e:
            self.log_time(f"❌ 銷售資訊查詢系統轉換過程發生錯誤：{str(e)}")
            raise


def main():
    """主程式入口"""
    import sys

    # 檢查命令列參數
    if len(sys.argv) > 1 and sys.argv[1] == "--daily":
        # 直接執行日常更新，不需要用戶輸入
        print("=" * 60)
        print("🔧 BI系統整合資料匯入工具 - 日常更新模式")
        print("=" * 60)

        try:
            converter = BIIntegratedDataConverter()
            converter.run_bi_daily_update()
            print("\n" + "=" * 60)
            print("✨ BI系統日常更新完成！")
        except Exception as e:
            print(f"\n❌ 發生錯誤：{str(e)}")
        return

    # 互動模式
    print("=" * 60)
    print("🔧 BI系統整合資料匯入工具")
    print("=" * 60)
    print("1. BI系統完整轉換（銷售資訊查詢 + 庫存盤點 + 庫存查詢系統）⭐ 推薦首次使用")
    print("2. BI系統日常更新（更新所有系統的日常變動資料）⭐ 推薦日常使用")
    print("3. 僅轉換銷售資訊查詢系統（客戶、銷售、維修、寄倉、服務登記卡）")
    print("4. 僅轉換庫存盤點系統（產品對照、倉別、盤點記錄）")
    print("5. 僅轉換庫存查詢系統（正航庫存資料）")
    print("6. 僅轉換客戶資料")
    print("7. 僅轉換銷售資料")
    print("8. 僅轉換維修資料")
    print("9. 僅轉換寄倉資料")
    print("10. 僅轉換服務登記卡資料")
    print("11. 僅轉換產品對照資料")
    print("12. 僅轉換倉別資料")
    print("13. 驗證所有系統資料完整性")
    print("=" * 60)
    print("💡 說明：")
    print("   模式1：首次使用或需要重建所有系統資料")
    print("   模式2：日常使用，更新所有系統的變動資料")
    print("   模式3-5：針對特定系統的完整轉換")
    print("   模式6-12：特定資料類型的轉換")
    print("   模式13：檢查所有系統的資料狀態")
    print("=" * 60)
    print("📁 資料來源目錄：D:\\WEB\\BI\\資料來源")
    print("=" * 60)

    try:
        choice = input("請選擇操作模式 (1-13): ").strip()

        converter = BIIntegratedDataConverter()

        if choice == "1":
            converter.run_bi_full_conversion()
        elif choice == "2":
            converter.run_bi_daily_update()
        elif choice == "3":
            converter.run_sales_only_conversion()
        elif choice == "4":
            converter.run_inventory_only_conversion()
        elif choice == "5":
            converter.convert_inventory_inquiry_data()
        elif choice == "6":
            converter.convert_customer_data()
        elif choice == "7":
            converter.convert_sales_data()
        elif choice == "8":
            converter.convert_repair_data()
        elif choice == "9":
            converter.convert_custody_data()
        elif choice == "10":
            converter.convert_service_card_data()
        elif choice == "11":
            converter.convert_inventory_products_data()
        elif choice == "12":
            converter.convert_inventory_warehouses_data()
        elif choice == "13":
            converter.verify_data_integrity()
        else:
            print("❌ 無效的選擇")
            return

        print("\n" + "=" * 60)
        print("✨ 操作完成！")

    except KeyboardInterrupt:
        print("\n❌ 操作被使用者中斷")
    except Exception as e:
        print(f"\n❌ 發生錯誤：{str(e)}")
    finally:
        try:
            input("\n按 Enter 鍵結束...")
        except EOFError:
            # 處理批次檔執行時的 EOF 錯誤
            pass


if __name__ == "__main__":
    main()
