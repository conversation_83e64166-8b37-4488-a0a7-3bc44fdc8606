# 🔧 BI系統整合資料匯入工具說明

## 📋 功能概述

BI系統整合資料匯入工具是一個統一的資料管理解決方案，支援所有BI分支系統的資料庫匯入和管理。

### ✅ 支援的系統

1. **銷售資訊查詢系統**
   - 客戶基本資料
   - 銷售資料
   - 維修資料
   - 寄倉資料
   - 服務登記卡

2. **庫存盤點系統**
   - 產品對照資料
   - 倉別資料
   - 盤點記錄資料庫

3. **庫存查詢系統** ⭐ 新增
   - 正航庫存資料
   - 支援產品名稱、倉庫名稱、倉庫往來對象名稱關鍵字查詢

## 🚀 使用方式

### 方法一：雙擊批次檔
```
雙擊執行：執行BI系統資料匯入.bat
```

### 方法二：命令列執行
```bash
python "BI系統整合資料匯入工具.py"
```

### 方法三：日常更新模式（自動化）
```bash
python "BI系統整合資料匯入工具.py" --daily
```

## 📊 操作模式

### 1. BI系統完整轉換 ⭐ 推薦首次使用
- 轉換所有系統的所有資料
- 包含銷售資訊查詢系統、庫存盤點系統、庫存查詢系統
- 適用於首次安裝或需要重建所有資料

### 2. BI系統日常更新 ⭐ 推薦日常使用
- 更新所有系統的日常變動資料
- 包含正航庫存資料的日常更新
- 節省時間，提升效率

### 3. 僅轉換銷售資訊查詢系統
- 僅處理銷售資訊查詢系統相關資料
- 包含客戶、銷售、維修、寄倉、服務登記卡資料

### 4. 僅轉換庫存盤點系統
- 僅處理庫存盤點系統相關資料
- 包含產品對照、倉別、盤點記錄資料庫初始化

### 5. 僅轉換庫存查詢系統 ⭐ 新增
- 僅處理正航庫存資料轉換
- 支援產品名稱、倉庫名稱、倉庫往來對象名稱關鍵字查詢
- 適用於日常庫存資料更新

### 6-12. 單項資料轉換
- 針對特定資料類型的轉換
- 適用於特殊需求或故障排除
- 包含：客戶資料、銷售資料、維修資料、寄倉資料、服務登記卡資料、產品對照資料、倉別資料

### 13. 驗證所有系統資料完整性
- 檢查所有系統的資料庫狀態
- 顯示各系統的資料筆數
- 包含庫存查詢系統的驗證

## 📁 檔案結構

```
BI/
├── BI系統整合資料匯入工具.py          # 主程式
├── 執行BI系統資料匯入.bat             # 批次執行檔
├── BI系統整合資料匯入工具說明.md       # 說明文件
├── 資料來源/                          # 統一資料來源目錄 ⭐ 重要
│   ├── 客戶資料.xlsx                  # 客戶基本資料
│   ├── 2025銷售資料.xlsx              # 當年度銷售資料
│   ├── 維修資料.xlsx                  # 維修服務資料
│   ├── 寄倉資料.xlsx                  # 寄倉業務資料
│   ├── 昇峰銷售資料.xlsx              # 昇峰歷史銷售資料
│   ├── 昇峰維修資料.xlsx              # 昇峰歷史維修資料
│   ├── 昇峰寄庫資料.xlsx              # 昇峰歷史寄庫資料
│   ├── 服務登記卡.xlsx                # 服務登記卡資料
│   ├── 產品對照資料.xlsx              # 產品QR Code對照表
│   └── 倉別.xlsx                      # 倉別代碼表
├── Sales_information_inquiry/         # 銷售資訊查詢系統
│   └── database/                      # 銷售系統資料庫目錄
│       ├── sales.db                   # 銷售資料庫
│       ├── repair.db                  # 維修資料庫
│       ├── custody.db                 # 寄倉資料庫
│       ├── customer_new.db            # 客戶資料庫
│       └── service_card.db            # 服務登記卡資料庫
└── inventory_system/                  # 庫存盤點系統
    └── database/                      # 庫存系統資料庫目錄
        ├── products.db                # 產品對照資料庫
        ├── warehouses.db              # 倉別資料庫
        └── inventory_records.db       # 盤點記錄資料庫
```

## 📊 資料庫對應

**📁 統一資料來源目錄：`D:\WEB\BI\資料來源`**

### 銷售資訊查詢系統
| 來源檔案 | 目標資料庫 | 說明 |
|---------|-----------|------|
| 資料來源/客戶資料.xlsx | Sales_information_inquiry/database/customer_new.db | 客戶基本資料 |
| 資料來源/2025銷售資料.xlsx + 昇峰銷售資料.xlsx | Sales_information_inquiry/database/sales.db | 銷售主檔和明細 |
| 資料來源/維修資料.xlsx + 昇峰維修資料.xlsx | Sales_information_inquiry/database/repair.db | 維修服務資料 |
| 資料來源/寄倉資料.xlsx + 昇峰寄庫資料.xlsx | Sales_information_inquiry/database/custody.db | 寄倉主檔和明細 |
| 資料來源/服務登記卡.xlsx | Sales_information_inquiry/database/service_card.db | 服務登記卡資料 |

### 庫存盤點系統
| 來源檔案 | 目標資料庫 | 說明 |
|---------|-----------|------|
| 資料來源/產品對照資料.xlsx | inventory_system/database/products.db | 產品基本資料和QR Code對照 |
| 資料來源/倉別.xlsx | inventory_system/database/warehouses.db | 倉別代碼和名稱 |
| - | inventory_system/database/inventory_records.db | 盤點記錄（系統自動建立） |

## ⚡ 建議使用流程

### 首次使用
1. 確保所有Excel檔案放在 `D:\WEB\BI\資料來源` 目錄中
2. 選擇「BI系統完整轉換」模式
3. 等待所有資料轉換完成
4. 檢查資料完整性

### 日常更新
1. 更新 `D:\WEB\BI\資料來源` 目錄中的相關Excel檔案
2. 選擇「BI系統日常更新」模式
3. 系統自動處理變動資料
4. 節省時間，提升效率

### 資料檔案管理
- **統一存放**：所有Excel資料檔案統一放在 `D:\WEB\BI\資料來源` 目錄
- **檔案命名**：請保持檔案名稱與系統預期一致
- **檔案更新**：直接替換資料來源目錄中的檔案即可

## 🔍 故障排除

### 常見問題

1. **檔案找不到**
   - 確認Excel檔案放在 `D:\WEB\BI\資料來源` 目錄中
   - 檢查檔案名稱是否正確
   - 確認檔案沒有被其他程式開啟

2. **轉換時間過長**
   - 使用「日常更新」模式
   - 確認沒有其他程式佔用資料庫檔案
   - 關閉不必要的程式釋放記憶體

3. **記憶體不足**
   - 關閉其他不必要的程式
   - 分批處理大型檔案
   - 重新啟動電腦釋放記憶體

4. **權限問題**
   - 以管理員身分執行
   - 確認對資料庫目錄有寫入權限
   - 檢查防毒軟體是否阻擋

### 效能提升建議

- 定期清理舊的資料庫檔案
- 使用SSD硬碟存放資料庫
- 確保有足夠的可用記憶體
- 定期重新啟動系統

## 📈 預期效能改善

相較於分別執行各系統的匯入工具：
- **操作便利性**：大幅提升，一次處理所有系統
- **資料一致性**：確保所有系統資料同步
- **維護效率**：統一管理，減少操作錯誤
- **時間節省**：批量處理，提升整體效率

## 🛠️ 技術細節

### 優化技術
- 統一的資料處理流程
- 批量執行SQL指令
- 優化日期時間轉換邏輯
- 改善索引建立策略
- 跨系統資料驗證

### 資料完整性
- 自動驗證轉換結果
- 提供詳細的處理日誌
- 支援錯誤回復機制
- 跨系統資料一致性檢查

## 📞 技術支援

如遇到技術問題，請聯繫系統管理員並提供：
- 錯誤訊息截圖
- 操作步驟描述
- 使用的檔案和資料情況
- 系統環境資訊

---

**版本：** 1.0  
**更新日期：** 2025-01-23  
**開發者：** BI系統開發團隊

**注意**：首次使用建議備份原始資料檔案，確保資料安全。
