<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盤點系統管理後台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }

        .card-header {
            background: linear-gradient(135deg, #dc3545, #6f42c1);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }

        .stat-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #dc3545, #6f42c1);
            border: none;
            border-radius: 10px;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #c82333, #5a2d91);
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h2><i class="bi bi-gear-fill"></i> 盤點系統管理後台</h2>
                    <small>管理員：{{ session['username'] }}</small>
                </div>
                <div class="col-auto">
                    <a href="{{ url_for('inventory.index') }}" class="btn btn-light btn-sm me-2">
                        <i class="bi bi-clipboard-check"></i> 盤點頁面
                    </a>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-sm">
                        <i class="bi bi-house"></i> 返回首頁
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 統計卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number" id="totalRecords">-</div>
                    <div>總盤點記錄</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number" id="todayRecords">-</div>
                    <div>今日盤點記錄</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">-</div>
                    <div>參與使用者數</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number" id="totalWarehouses">-</div>
                    <div>涉及倉別數</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 匯出功能 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-download"></i> 匯出盤點結果
                    </div>
                    <div class="card-body">
                        <form id="exportForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="startDate" class="form-label">開始日期：</label>
                                    <input type="date" id="startDate" class="form-control">
                                </div>
                                <div class="col-md-6">
                                    <label for="endDate" class="form-label">結束日期：</label>
                                    <input type="date" id="endDate" class="form-control">
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-file-earmark-excel"></i> 匯出Excel檔案
                                </button>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    * 如不選擇日期範圍，將匯出所有記錄
                                </small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 每日統計圖表 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-bar-chart"></i> 最近7天盤點統計
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 使用者統計 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-people"></i> 使用者盤點統計
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>使用者帳號</th>
                                        <th>盤點次數</th>
                                        <th>佔比</th>
                                    </tr>
                                </thead>
                                <tbody id="userStatsTable">
                                    <tr>
                                        <td colspan="3" class="text-center">載入中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 倉別統計 -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-building"></i> 倉別盤點統計
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>倉別代碼</th>
                                        <th>倉別名稱</th>
                                        <th>盤點次數</th>
                                    </tr>
                                </thead>
                                <tbody id="warehouseStatsTable">
                                    <tr>
                                        <td colspan="3" class="text-center">載入中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let dailyChart = null;

        // 頁面載入完成後初始化
        document.addEventListener('DOMContentLoaded', function () {
            loadStats();

            // 設定預設日期範圍（最近7天）
            const today = new Date();
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = weekAgo.toISOString().split('T')[0];
        });

        // 載入統計資料
        function loadStats() {
            fetch('/inventory/get_inventory_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('載入統計資料失敗：' + data.error);
                        return;
                    }

                    // 更新統計卡片
                    document.getElementById('totalRecords').textContent = data.total_records || 0;
                    document.getElementById('todayRecords').textContent = data.today_records || 0;
                    document.getElementById('totalUsers').textContent = data.user_stats.length || 0;
                    document.getElementById('totalWarehouses').textContent = data.warehouse_stats.length || 0;

                    // 更新使用者統計表
                    updateUserStatsTable(data.user_stats, data.total_records);

                    // 更新倉別統計表
                    updateWarehouseStatsTable(data.warehouse_stats);

                    // 更新每日統計圖表
                    updateDailyChart(data.daily_stats);
                })
                .catch(error => {
                    console.error('載入統計資料錯誤:', error);
                    alert('載入統計資料時發生錯誤');
                });
        }

        // 更新使用者統計表
        function updateUserStatsTable(userStats, totalRecords) {
            const tbody = document.getElementById('userStatsTable');

            if (!userStats || userStats.length === 0) {
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暫無資料</td></tr>';
                return;
            }

            let html = '';
            userStats.forEach(stat => {
                const percentage = totalRecords > 0 ? ((stat.count / totalRecords) * 100).toFixed(1) : 0;
                html += `
                    <tr>
                        <td><strong>${stat.user_id}</strong></td>
                        <td><span class="badge bg-primary">${stat.count}</span></td>
                        <td>${percentage}%</td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // 更新倉別統計表
        function updateWarehouseStatsTable(warehouseStats) {
            const tbody = document.getElementById('warehouseStatsTable');

            if (!warehouseStats || warehouseStats.length === 0) {
                tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暫無資料</td></tr>';
                return;
            }

            let html = '';
            warehouseStats.forEach(stat => {
                html += `
                    <tr>
                        <td><strong>${stat.warehouse_code}</strong></td>
                        <td>${stat.warehouse_name}</td>
                        <td><span class="badge bg-success">${stat.count}</span></td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // 更新每日統計圖表
        function updateDailyChart(dailyStats) {
            const ctx = document.getElementById('dailyChart').getContext('2d');

            // 如果圖表已存在，先銷毀
            if (dailyChart) {
                dailyChart.destroy();
            }

            const labels = [];
            const data = [];

            // 準備圖表資料
            if (dailyStats && dailyStats.length > 0) {
                dailyStats.reverse().forEach(stat => {
                    const date = new Date(stat.inventory_date);
                    labels.push(date.toLocaleDateString('zh-TW', { month: 'short', day: 'numeric' }));
                    data.push(stat.count);
                });
            }

            dailyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '盤點次數',
                        data: data,
                        borderColor: 'rgb(220, 53, 69)',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 匯出表單提交
        document.getElementById('exportForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (startDate && endDate && startDate > endDate) {
                alert('開始日期不能晚於結束日期');
                return;
            }

            // 顯示載入狀態
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 匯出中...';
            submitBtn.disabled = true;

            // 建立表單資料
            const formData = new FormData();
            formData.append('start_date', startDate);
            formData.append('end_date', endDate);

            // 使用fetch下載檔案
            fetch('/inventory/export_inventory', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    start_date: startDate,
                    end_date: endDate
                })
            })
                .then(response => {
                    if (response.ok) {
                        // 檢查回應類型
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            // 如果是JSON回應（錯誤情況）
                            return response.json().then(data => {
                                throw new Error(data.error || '匯出失敗');
                            });
                        } else {
                            // 如果是檔案回應
                            return response.blob().then(blob => {
                                // 建立下載連結
                                const url = window.URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.style.display = 'none';
                                a.href = url;

                                // 從回應標頭獲取檔案名稱，或使用預設名稱
                                const disposition = response.headers.get('content-disposition');
                                let filename = '盤點結果.xlsx';
                                if (disposition) {
                                    const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(disposition);
                                    if (matches != null && matches[1]) {
                                        filename = matches[1].replace(/['"]/g, '');
                                    }
                                } else {
                                    // 產生時間戳記檔名
                                    const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_');
                                    filename = `盤點結果_${timestamp}.xlsx`;
                                }

                                a.download = filename;
                                document.body.appendChild(a);
                                a.click();
                                window.URL.revokeObjectURL(url);
                                document.body.removeChild(a);

                                alert('匯出成功！檔案已開始下載。');
                            });
                        }
                    } else {
                        throw new Error('匯出請求失敗');
                    }
                })
                .catch(error => {
                    console.error('匯出錯誤:', error);
                    alert('匯出失敗：' + error.message);
                })
                .finally(() => {
                    // 恢復按鈕狀態
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
        });
    </script>
</body>

</html>