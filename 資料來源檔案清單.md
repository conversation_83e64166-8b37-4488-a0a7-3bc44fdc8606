# 📁 資料來源檔案清單

## 📍 資料來源目錄位置
```
D:\WEB\BI\資料來源
```

## 📋 必要檔案清單

### 🏢 銷售資訊查詢系統檔案

| 檔案名稱 | 用途 | 更新頻率 | 必要性 |
|---------|------|---------|--------|
| `客戶資料.xlsx` | 客戶基本資料、聯絡資訊 | 每日 | ✅ 必要 |
| `2025銷售資料.xlsx` | 當年度銷售交易記錄 | 每日 | ✅ 必要 |
| `維修資料.xlsx` | 維修服務記錄 | 每日 | ✅ 必要 |
| `寄倉資料.xlsx` | 寄倉業務記錄 | 每日 | ✅ 必要 |
| `昇峰銷售資料.xlsx` | 昇峰歷史銷售資料 | 不常更新 | 🔶 歷史資料 |
| `昇峰維修資料.xlsx` | 昇峰歷史維修資料 | 不常更新 | 🔶 歷史資料 |
| `昇峰寄庫資料.xlsx` | 昇峰歷史寄庫資料 | 不常更新 | 🔶 歷史資料 |
| `服務登記卡.xlsx` | 服務登記卡資料 | 每週 | ✅ 必要 |

### 📦 庫存盤點系統檔案

| 檔案名稱 | 用途 | 更新頻率 | 必要性 |
|---------|------|---------|--------|
| `產品對照資料.xlsx` | 產品QR Code對照表 | 每月 | ✅ 必要 |
| `倉別.xlsx` | 倉別代碼和名稱 | 很少更新 | ✅ 必要 |

### 📋 庫存查詢系統檔案

| 檔案名稱 | 用途 | 更新頻率 | 必要性 |
|---------|------|---------|--------|
| `正航庫存資料.xlsx` | 正航系統庫存資料 | 每日 | ✅ 必要 |

## 📊 檔案內容說明

### 客戶資料.xlsx
- **內容**：客戶代碼、客戶名稱、聯絡地址、電話、業務人員等
- **格式**：標準Excel格式，包含表頭
- **注意事項**：客戶代碼不可重複

### 2025銷售資料.xlsx
- **內容**：單據編號、發貨日期、客戶資訊、產品明細、金額等
- **格式**：包含主檔和明細資料
- **注意事項**：日期格式需正確

### 維修資料.xlsx
- **內容**：維修單據、客戶資訊、服務地址、維修項目等
- **格式**：標準Excel格式
- **注意事項**：客戶欄位避免浮點數格式

### 寄倉資料.xlsx
- **內容**：寄倉單據、借貨對象、產品明細、數量等
- **格式**：包含主檔和明細資料
- **注意事項**：數量欄位需為數值格式

### 服務登記卡.xlsx
- **內容**：服務登記號、核算組織、客戶代碼、客戶名稱、聯絡人、聯絡電話、服務地址、產品型號、產品序號、服務項目、服務人員、登記日期、服務日期、完成日期、服務狀態、備註、保固期限、裝機位置說明
- **格式**：Excel格式，前兩行為標題，從第三行開始為資料，共18個欄位
- **注意事項**：系統會自動跳過標題行並重新命名欄位，日期欄位會自動轉換格式

### 產品對照資料.xlsx
- **內容**：QR Code、產品名稱、產品代碼、規格、單位等
- **格式**：5欄資料，依序為QR Code、產品名稱、產品代碼、規格、單位
- **注意事項**：QR Code不可重複

### 倉別.xlsx
- **內容**：倉別代碼、倉別名稱、說明
- **格式**：3欄資料，依序為倉別代碼、倉別名稱、說明
- **注意事項**：倉別代碼不可重複

### 正航庫存資料.xlsx
- **內容**：產品名稱、倉庫名稱、倉庫往來對象名稱、產品代碼、規格、單位、數量、單價、總金額、最後更新日期、備註
- **格式**：Excel格式，包含完整的庫存資訊
- **注意事項**：支援產品名稱、倉庫名稱、倉庫往來對象名稱的關鍵字查詢，數量和金額欄位需為數值格式

## 🔄 檔案更新流程

### 日常更新檔案
1. `客戶資料.xlsx` - 每日更新
2. `2025銷售資料.xlsx` - 每日更新
3. `維修資料.xlsx` - 每日更新
4. `寄倉資料.xlsx` - 每日更新
5. `正航庫存資料.xlsx` - 每日更新 ⭐ 新增

### 定期更新檔案
1. `產品對照資料.xlsx` - 每月檢查更新
2. `倉別.xlsx` - 有新倉別時更新
3. `服務登記卡.xlsx` - 每週更新

### 歷史資料檔案
1. `昇峰銷售資料.xlsx` - 通常不需更新
2. `昇峰維修資料.xlsx` - 通常不需更新
3. `昇峰寄庫資料.xlsx` - 通常不需更新

## ⚠️ 注意事項

### 檔案命名
- 檔案名稱必須完全一致（包含大小寫）
- 不可更改檔案名稱
- 副檔名必須是 `.xlsx`

### 檔案格式
- 使用Excel 2007以上版本格式（.xlsx）
- 確保檔案沒有密碼保護
- 避免檔案損壞或格式錯誤

### 檔案存取
- 執行匯入工具時，請關閉所有Excel檔案
- 確保檔案沒有被其他程式佔用
- 建議定期備份重要檔案

### 資料品質
- 確保日期格式正確
- 避免空白或無效的關鍵欄位
- 檢查數值欄位的格式

## 🛠️ 故障排除

### 檔案找不到
1. 檢查檔案是否在正確目錄：`D:\WEB\BI\資料來源`
2. 確認檔案名稱完全正確
3. 檢查檔案是否存在

### 檔案無法讀取
1. 確認檔案格式為 `.xlsx`
2. 檢查檔案是否損壞
3. 確認檔案沒有密碼保護
4. 關閉所有開啟的Excel程式

### 資料轉換錯誤
1. 檢查檔案內容格式
2. 確認必要欄位存在
3. 檢查日期和數值格式
4. 查看錯誤訊息進行修正

---

**更新日期：** 2025-01-23  
**版本：** 1.0
