# 📋 庫存查詢系統

## 🎯 系統概述

庫存查詢系統是BI系統的重要組成部分，提供便捷的庫存資料查詢功能，支援多種查詢條件和響應式設計，適用於手機、電腦、平板等各種設備。

## ✅ 系統功能

### 🔐 安全性
- **登入驗證**：必須通過帳號密碼登入才能使用
- **Session管理**：防止直接使用網址訪問
- **權限控制**：與主系統統一的使用者管理

### 🔍 查詢功能
- **產品名稱關鍵字查詢**：支援模糊搜尋
- **倉庫往來對象名稱關鍵字查詢**：支援模糊搜尋
- **存貨屬性篩選**：
  - 世磊（自有庫存）
  - 寄倉（借入庫存）
  - 借出（借出庫存）
- **自動過濾零庫存**：數量為0的記錄不會顯示在查詢結果中

### 📊 顯示功能
- **查詢結果欄位**：
  - 產品名稱（支援表格內篩選）
  - 倉庫名稱（支援表格內篩選）
  - 存貨屬性
  - 倉庫往來對象名稱
  - 庫存數量
- **表格篩選功能**：
  - 產品名稱下拉篩選：從查詢結果中的產品清單選擇特定產品
  - 倉庫名稱下拉篩選：從查詢結果中的倉庫清單選擇特定倉庫

### 📱 響應式設計
- **多設備支援**：手機、電腦、平板
- **自適應佈局**：根據螢幕大小調整介面
- **觸控友善**：適合觸控操作

## 🗂️ 檔案結構

```
Inventory_inquiry_system/
├── inventory_inquiry.py          # 主程式（Flask Blueprint）
├── README.md                     # 說明文件
├── templates/
│   └── inventory_inquiry.html    # 查詢頁面模板
├── static/
│   ├── css/                      # 樣式檔案目錄
│   └── js/                       # JavaScript檔案目錄
└── database/
    └── inventory_data.db         # 庫存資料庫
```

## 🚀 使用方式

### 1. 系統訪問
1. 登入BI系統主頁面
2. 在主控台點選「庫存查詢」連結
3. 進入庫存查詢頁面

### 2. 查詢操作
1. **產品名稱查詢**：輸入產品名稱關鍵字
2. **往來對象查詢**：輸入倉庫往來對象名稱關鍵字
3. **存貨屬性篩選**：選擇特定的存貨屬性
4. 點選「查詢庫存」按鈕
5. 查看查詢結果

### 3. 表格內篩選
1. **產品名稱篩選**：在查詢結果的產品名稱欄位下拉清單中選擇特定產品
2. **倉庫名稱篩選**：在查詢結果的倉庫名稱欄位下拉清單中選擇特定倉庫
3. **動態選項**：下拉清單選項來自當前查詢結果的實際資料
4. **即時篩選**：選擇時立即篩選顯示的結果

### 4. 清除條件
- 點選「清除條件」按鈕重置所有查詢條件和表格篩選

## 📊 存貨屬性說明

| 屬性 | 說明 | 判斷條件 |
|------|------|----------|
| 世磊 | 自有庫存 | 倉庫往來對象名稱包含"世磊"或為空，且數量不為0 |
| 寄倉 | 借入庫存 | 倉庫往來對象名稱不包含"世磊"且不為空，數量為正數 |
| 借出 | 借出庫存 | 庫存數量為負數 |

**注意**：所有查詢結果都會自動過濾掉數量為0的記錄，確保只顯示有實際庫存的產品。

## 🔧 技術架構

### 後端技術
- **Flask Blueprint**：模組化設計
- **SQLite資料庫**：高效能資料存取
- **SQL索引優化**：提升查詢效能

### 前端技術
- **響應式CSS**：支援多種設備
- **JavaScript**：動態互動功能
- **AJAX**：非同步資料查詢

### 資料庫設計
- **主鍵索引**：id欄位
- **查詢索引**：product_name, warehouse_partner_name
- **複合索引**：提升多條件查詢效能

## 📈 效能優化

### 資料庫優化
- **索引策略**：針對查詢欄位建立索引
- **分批查詢**：避免大量資料載入
- **SQL優化**：使用高效的查詢語句

### 前端優化
- **載入提示**：查詢過程中顯示載入狀態
- **結果分頁**：大量資料的分頁顯示
- **快取機制**：統計資訊快取

## 🔄 資料更新

### 自動更新
- 透過BI系統整合資料匯入工具自動更新
- 支援日常更新模式
- 資料來源：`D:\WEB\BI\資料來源\正航庫存資料.xlsx`

### 手動更新
```bash
# 僅更新庫存查詢系統
python "BI系統整合資料匯入工具.py"
選擇：5. 僅轉換庫存查詢系統（正航庫存資料）
```

## 🛠️ 維護指南

### 日常維護
1. **資料備份**：定期備份inventory_data.db
2. **效能監控**：監控查詢回應時間
3. **日誌檢查**：檢查系統錯誤日誌

### 故障排除
1. **查詢無結果**：檢查資料庫是否有資料
2. **載入緩慢**：檢查資料庫索引是否正常
3. **登入問題**：確認Session狀態

## 📱 行動裝置使用

### 手機使用
- 支援觸控操作
- 自動調整欄位寬度
- 優化按鈕大小

### 平板使用
- 充分利用螢幕空間
- 保持桌面版功能
- 觸控友善設計

## 🔮 未來擴展

### 功能擴展
- 庫存異動記錄查詢
- 庫存預警功能
- 資料匯出功能
- 圖表統計分析

### 技術升級
- 資料庫效能優化
- 前端框架升級
- API介面開發
- 行動App開發

---

**開發日期：** 2025-01-23  
**版本：** 1.0  
**開發者：** BI系統開發團隊
