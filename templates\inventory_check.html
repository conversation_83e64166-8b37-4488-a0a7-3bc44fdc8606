<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>盤點系統</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .container { max-width: 800px; }
        .history-table { margin-top: 20px; }
        .scan-button {
            margin-left: 10px;
        }
        #itemInfo {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        #itemInfo p {
            margin-bottom: 5px;
            font-size: 1.1em;
        }
        #itemInfo span {
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2 class="mb-4">盤點系統</h2>
        
        <!-- 掃描區域 -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">掃描條碼</h5>
                <div class="mb-3 d-flex">
                    <input type="text" id="barcode" class="form-control" placeholder="請掃描或輸入條碼" autofocus>
                    <button onclick="startScan()" class="btn btn-secondary scan-button">開啟掃描</button>
                </div>
                <div id="itemInfo" class="mb-3" style="display: none;">
                    <p>品項：<span id="itemName"></span></p>
                    <p>倉庫：<span id="warehouse"></span></p>
                </div>
                <div class="mb-3">
                    <input type="number" id="quantity" class="form-control" placeholder="請輸入數量">
                </div>
                <button onclick="submitCheck()" class="btn btn-primary">送出</button>
            </div>
        </div>

        <!-- 歷史記錄 -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">盤點記錄</h5>
                    <div id="exportButton" style="display: none;">
                        <button onclick="exportData()" class="btn btn-success">匯出資料</button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>條碼</th>
                                <th>品項</th>
                                <th>倉庫</th>
                                <th>數量</th>
                                <th>建立時間</th>
                                <th>更新時間</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="historyTable">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/html5-qrcode"></script>
    <script>
        let html5QrcodeScanner = null;

        // 檢查是否為C4D002帳號
        function checkUserRole() {
            const username = '{{ session.username }}';
            if (username === 'C4D002') {
                document.getElementById('exportButton').style.display = 'block';
            }
        }

        // 開啟掃描功能
        function startScan() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
            }

            html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader", { fps: 10, qrbox: 250 });
            
            html5QrcodeScanner.render((decodedText) => {
                document.getElementById('barcode').value = decodedText;
                document.getElementById('barcode').dispatchEvent(new Event('change'));
                html5QrcodeScanner.clear();
            });
        }

        // 掃描條碼後自動獲取商品資訊
        document.getElementById('barcode').addEventListener('change', async function() {
            const barcode = this.value;
            if (barcode) {
                try {
                    const response = await fetch(`/get_item_info/${barcode}`);
                    const data = await response.json();
                    if (data.success) {
                        document.getElementById('itemInfo').style.display = 'block';
                        document.getElementById('itemName').textContent = data.item_name;
                        document.getElementById('warehouse').textContent = data.warehouse;
                        // 自動聚焦到數量輸入框
                        document.getElementById('quantity').focus();
                    } else {
                        alert('找不到商品資訊');
                        document.getElementById('itemInfo').style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('獲取商品資訊時發生錯誤');
                }
            }
        });

        // 匯出資料
        async function exportData() {
            const response = await fetch('/export_inventory_checks');
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `盤點資料_${new Date().toLocaleDateString()}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        // 提交盤點資料
        async function submitCheck() {
            const barcode = document.getElementById('barcode').value;
            const quantity = document.getElementById('quantity').value;
            
            if (!barcode || !quantity) {
                alert('請輸入條碼和數量');
                return;
            }

            try {
                const response = await fetch('/add_inventory_check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        barcode: barcode,
                        quantity: parseInt(quantity)
                    })
                });

                const data = await response.json();
                if (data.success) {
                    alert('盤點資料已儲存');
                    loadHistory();
                    // 清空輸入欄位
                    document.getElementById('barcode').value = '';
                    document.getElementById('quantity').value = '';
                    document.getElementById('itemInfo').style.display = 'none';
                    // 重新聚焦到條碼輸入框
                    document.getElementById('barcode').focus();
                } else {
                    alert('儲存失敗：' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('儲存資料時發生錯誤');
            }
        }

        // 載入歷史記錄
        async function loadHistory() {
            try {
                const response = await fetch('/get_inventory_checks');
                const data = await response.json();
                
                const tbody = document.getElementById('historyTable');
                tbody.innerHTML = '';
                
                data.checks.forEach(check => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${check.barcode}</td>
                        <td>${check.item_name}</td>
                        <td>${check.warehouse}</td>
                        <td>
                            <input type="number" value="${check.quantity}" 
                                   onchange="updateQuantity(${check.id}, this.value)">
                        </td>
                        <td>${check.created_at}</td>
                        <td>${check.updated_at}</td>
                        <td>
                            <button onclick="deleteCheck(${check.id})" 
                                    class="btn btn-danger btn-sm">刪除</button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });
            } catch (error) {
                console.error('Error:', error);
                alert('載入歷史記錄時發生錯誤');
            }
        }

        // 更新數量
        async function updateQuantity(id, quantity) {
            try {
                const response = await fetch('/update_inventory_check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: id,
                        quantity: parseInt(quantity)
                    })
                });

                const data = await response.json();
                if (data.success) {
                    loadHistory();
                } else {
                    alert('更新失敗：' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('更新數量時發生錯誤');
            }
        }

        // 刪除記錄
        async function deleteCheck(id) {
            if (!confirm('確定要刪除這筆記錄嗎？')) {
                return;
            }

            try {
                const response = await fetch('/delete_inventory_check', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: id
                    })
                });

                const data = await response.json();
                if (data.success) {
                    loadHistory();
                } else {
                    alert('刪除失敗：' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('刪除記錄時發生錯誤');
            }
        }

        // 頁面載入時檢查使用者角色並載入歷史記錄
        document.addEventListener('DOMContentLoaded', () => {
            loadHistory();
            checkUserRole();
            // 自動聚焦到條碼輸入框
            document.getElementById('barcode').focus();
        });
    </script>
</body>
</html> 