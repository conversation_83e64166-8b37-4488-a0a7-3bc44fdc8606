<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>庫存查詢系統</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .search-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .search-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 120px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }



        .results-section {
            padding: 0 30px 30px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .results-table th,
        .results-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .results-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .results-table tr:hover {
            background: #f5f5f5;
        }

        .inventory-type {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }

        .type-own {
            background: #d4edda;
            color: #155724;
        }

        .type-borrowed {
            background: #d1ecf1;
            color: #0c5460;
        }

        .type-lent {
            background: #f8d7da;
            color: #721c24;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .filter-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 5px;
            background-color: white;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .filter-header {
            position: relative;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .search-section {
                padding: 20px;
            }

            .search-form {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .search-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }



            .results-table {
                font-size: 14px;
            }

            .results-table th,
            .results-table td {
                padding: 10px 8px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.5em;
            }



            .results-table {
                font-size: 12px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>📋 庫存查詢系統</h1>
            <p>支援產品名稱、倉庫往來對象名稱關鍵字查詢</p>
            <p style="font-size: 0.9em; opacity: 0.9;">💡 系統自動過濾數量為0的記錄，僅顯示有庫存的產品</p>
        </div>

        <div class="search-section">
            <form id="searchForm" class="search-form">
                <div class="form-group">
                    <label for="product_name">產品名稱</label>
                    <input type="text" id="product_name" name="product_name" placeholder="請輸入產品名稱關鍵字">
                </div>

                <div class="form-group">
                    <label for="warehouse_partner">倉庫往來對象名稱</label>
                    <input type="text" id="warehouse_partner" name="warehouse_partner" placeholder="請輸入往來對象名稱關鍵字">
                </div>

                <div class="form-group">
                    <label for="inventory_type">存貨屬性</label>
                    <select id="inventory_type" name="inventory_type">
                        <option value="">全部</option>
                        <option value="世磊">世磊（自有庫存）</option>
                        <option value="寄倉">寄倉（借入庫存）</option>
                        <option value="借出">借出（借出庫存）</option>
                    </select>
                </div>
            </form>

            <div class="search-buttons">
                <button type="button" class="btn btn-primary" onclick="searchInventory()">🔍 查詢庫存</button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">🗑️ 清除條件</button>
            </div>


        </div>

        <div class="results-section">
            <div id="loading" class="loading" style="display: none;">
                <p>🔄 查詢中，請稍候...</p>
            </div>

            <div id="results" style="display: none;">
                <table class="results-table">
                    <thead>
                        <tr>
                            <th class="filter-header">
                                產品名稱
                                <select class="filter-select" id="productNameFilter" onchange="filterTable()">
                                    <option value="">全部產品</option>
                                </select>
                            </th>
                            <th class="filter-header">
                                倉庫名稱
                                <select class="filter-select" id="warehouseNameFilter" onchange="filterTable()">
                                    <option value="">全部倉庫</option>
                                </select>
                            </th>
                            <th>存貨屬性</th>
                            <th>倉庫往來對象名稱</th>
                            <th>庫存數量</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                    </tbody>
                </table>
            </div>

            <div id="noResults" class="no-results" style="display: none;">
                <p>📭 沒有找到符合條件的庫存資料</p>
                <p>請嘗試調整查詢條件</p>
            </div>
        </div>
    </div>

    <script>


        // 查詢庫存
        function searchInventory() {
            const form = document.getElementById('searchForm');
            const formData = new FormData(form);

            // 顯示載入中
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';

            fetch('/inventory_inquiry/search', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';

                    if (data.success) {
                        if (data.data.length > 0) {
                            displayResults(data.data);
                        } else {
                            document.getElementById('noResults').style.display = 'block';
                        }
                    } else {
                        alert('查詢失敗: ' + data.error);
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('查詢失敗:', error);
                    alert('查詢失敗，請稍後再試');
                });
        }

        // 顯示查詢結果
        function displayResults(data) {
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';

            // 收集唯一的產品名稱和倉庫名稱
            const productNames = new Set();
            const warehouseNames = new Set();

            data.forEach(item => {
                const row = document.createElement('tr');

                // 收集唯一值
                productNames.add(item.product_name);
                warehouseNames.add(item.warehouse_name);

                // 存貨屬性樣式
                let typeClass = '';
                if (item.inventory_type === '世磊') typeClass = 'type-own';
                else if (item.inventory_type === '寄倉') typeClass = 'type-borrowed';
                else if (item.inventory_type === '借出') typeClass = 'type-lent';

                row.innerHTML = `
                    <td>${item.product_name}</td>
                    <td>${item.warehouse_name}</td>
                    <td><span class="inventory-type ${typeClass}">${item.inventory_type}</span></td>
                    <td>${item.warehouse_partner_name}</td>
                    <td>${item.quantity.toLocaleString()}</td>
                `;

                tbody.appendChild(row);
            });

            // 填充下拉清單
            populateFilterDropdowns(productNames, warehouseNames);

            document.getElementById('results').style.display = 'block';
        }

        // 填充篩選下拉清單
        function populateFilterDropdowns(productNames, warehouseNames) {
            // 填充產品名稱下拉清單
            const productSelect = document.getElementById('productNameFilter');
            productSelect.innerHTML = '<option value="">全部產品</option>';
            Array.from(productNames).sort().forEach(name => {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name;
                productSelect.appendChild(option);
            });

            // 填充倉庫名稱下拉清單
            const warehouseSelect = document.getElementById('warehouseNameFilter');
            warehouseSelect.innerHTML = '<option value="">全部倉庫</option>';
            Array.from(warehouseNames).sort().forEach(name => {
                const option = document.createElement('option');
                option.value = name;
                option.textContent = name;
                warehouseSelect.appendChild(option);
            });
        }

        // 清除表單
        function clearForm() {
            document.getElementById('searchForm').reset();
            document.getElementById('results').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';
            // 清除篩選輸入框
            document.getElementById('productNameFilter').value = '';
            document.getElementById('warehouseNameFilter').value = '';
        }

        // 表格篩選功能
        function filterTable() {
            const productNameFilter = document.getElementById('productNameFilter').value;
            const warehouseNameFilter = document.getElementById('warehouseNameFilter').value;
            const tbody = document.getElementById('resultsBody');
            const rows = tbody.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                const productNameCell = row.getElementsByTagName('td')[0];
                const warehouseNameCell = row.getElementsByTagName('td')[1];

                if (productNameCell && warehouseNameCell) {
                    const productName = productNameCell.textContent;
                    const warehouseName = warehouseNameCell.textContent;

                    const productMatch = productNameFilter === '' || productName === productNameFilter;
                    const warehouseMatch = warehouseNameFilter === '' || warehouseName === warehouseNameFilter;

                    if (productMatch && warehouseMatch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            }
        }

        // 頁面載入時執行
        document.addEventListener('DOMContentLoaded', function () {
            // 支援Enter鍵查詢
            document.getElementById('searchForm').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchInventory();
                }
            });
        });
    </script>
</body>

</html>