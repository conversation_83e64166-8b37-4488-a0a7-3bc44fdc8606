<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主選單</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .welcome {
            font-size: 1.2rem;
        }

        .logout {
            color: #dc3545;
            text-decoration: none;
        }

        .menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .menu-item {
            background-color: #007bff;
            color: white;
            padding: 1rem;
            text-align: center;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .menu-item:hover {
            background-color: #0056b3;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="welcome">歡迎，{{ session['name'] if session['name'] else session['username'] }}</div>
            <a href="{{ url_for('logout') }}" class="logout">登出</a>
        </div>
        <div class="menu">
            <a href="{{ url_for('sales') }}" class="menu-item">業務查詢</a>
            <a href="{{ url_for('assistant') }}" class="menu-item">助理查詢</a>
            <a href="{{ url_for('sales_info.index') }}" class="menu-item">銷售資訊查詢</a>
            <a href="{{ url_for('inventory.index') }}" class="menu-item">盤點系統</a>
            <a href="{{ url_for('inventory_inquiry.index') }}" class="menu-item"
                style="background-color: #28a745;">庫存查詢</a>
            {% if session['username'] == 'C4D002' %}
            <a href="{{ url_for('inventory.admin') }}" class="menu-item" style="background-color: #dc3545;">盤點管理後台</a>
            {% endif %}
        </div>
    </div>
</body>

</html>