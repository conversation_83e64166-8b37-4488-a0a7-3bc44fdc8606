<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>

    <!-- handlers：使用實際 python.exe + wfastcgi.py 路徑 -->
    <handlers accessPolicy="Read, Execute, Script">
      <add name="PythonHandler" path="*" verb="*" modules="FastCgiModule"
           scriptProcessor="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe|C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\wfastcgi.py"
           resourceType="Unspecified" requireAccess="Script" />
    </handlers>

    <defaultDocument enabled="true">
      <files>
        <clear/>
        <add value="index.html" />
      </files>
    </defaultDocument>

    <directoryBrowse enabled="false" />

    <security>
      <requestFiltering allowDoubleEscaping="true" />
    </security>

    <!-- FastCGI 註冊 -->
    <fastCgi>
      <application fullPath="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"
                   arguments="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\wfastcgi.py">
        <environmentVariables>
          <environmentVariable name="PYTHONPATH" value="D:\WEB\BI" />
          <environmentVariable name="WSGI_HANDLER" value="app.application" />
          <environmentVariable name="WSGI_LOG" value="D:\WEB\BI\wfastcgi.log" />
        </environmentVariables>
      </application>
    </fastCgi>

  </system.webServer>
</configuration>
