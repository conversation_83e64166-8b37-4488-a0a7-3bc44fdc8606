<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜尋功能測試</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .console-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>🔍 搜尋功能測試</h2>
        
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="testKeyword" class="form-label">搜尋關鍵字</label>
                    <input type="text" id="testKeyword" class="form-control" placeholder="輸入搜尋關鍵字" value="嘉義市西區松江三街129號">
                </div>
                
                <button onclick="testSearch()" class="btn btn-primary">🔍 測試搜尋</button>
                <button onclick="clearLog()" class="btn btn-secondary ms-2">🗑️ 清除日誌</button>
                
                <div id="result" class="mt-4"></div>
            </div>
            
            <div class="col-md-4">
                <h5>📋 測試日誌</h5>
                <div id="console" class="console-log"></div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const console = document.getElementById('console');
            const time = new Date().toLocaleTimeString();
            console.innerHTML += `[${time}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('console').innerHTML = '';
        }
        
        async function testSearch() {
            const keyword = document.getElementById('testKeyword').value;
            const resultDiv = document.getElementById('result');
            
            log('🚀 開始搜尋測試');
            log(`關鍵字: ${keyword}`);
            
            resultDiv.innerHTML = '<div class="alert alert-info">🔄 搜尋中...</div>';
            
            try {
                // 使用相對路徑，避免 CORS 問題
                const url = `/sales_info/search?keyword=${encodeURIComponent(keyword)}`;
                log(`請求URL: ${url}`);
                
                const response = await fetch(url);
                log(`回應狀態: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ 成功取得資料: ${data.length} 筆`);
                
                if (data.error) {
                    log(`❌ API 錯誤: ${data.error}`);
                    resultDiv.innerHTML = `<div class="alert alert-danger">錯誤: ${data.error}</div>`;
                    return;
                }
                
                if (data.length === 0) {
                    log('⚠️ 沒有找到資料');
                    resultDiv.innerHTML = '<div class="alert alert-warning">沒有找到資料</div>';
                    return;
                }
                
                // 顯示結果
                log('📊 開始渲染結果');
                let html = `<div class="alert alert-success">✅ 找到 ${data.length} 筆資料</div>`;
                html += '<div class="table-responsive">';
                html += '<table class="table table-bordered table-striped">';
                html += '<thead class="table-dark"><tr><th>類型</th><th>客戶名稱</th><th>地址</th><th>日期</th><th>詳情</th></tr></thead>';
                html += '<tbody>';
                
                data.forEach((item, index) => {
                    const d = item.data;
                    const typeMap = {
                        'customer': { name: '客戶資料', class: 'bg-info' },
                        'sales': { name: '銷售', class: 'bg-primary' },
                        'repair': { name: '維修', class: 'bg-success' },
                        'custody': { name: '寄倉出貨', class: 'bg-warning text-dark' }
                    };
                    
                    const typeInfo = typeMap[item.type] || { name: item.type, class: 'bg-secondary' };
                    
                    html += `<tr>
                        <td><span class="badge ${typeInfo.class}">${typeInfo.name}</span></td>
                        <td>${d.客戶名稱 || ''}</td>
                        <td>${d.聯絡地址 || d.送貨地址 || d.服務地址 || ''}</td>
                        <td>${d.發貨日期 || d.單據日期 || d.出勤開始時間 || ''}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="showDetails(${index})">
                                📋 詳情
                            </button>
                        </td>
                    </tr>`;
                });
                
                html += '</tbody></table></div>';
                
                // 儲存資料供詳情查看使用
                window.searchResults = data;
                
                resultDiv.innerHTML = html;
                log('✅ 結果渲染完成');
                
            } catch (error) {
                log(`❌ 搜尋失敗: ${error.message}`);
                resultDiv.innerHTML = `<div class="alert alert-danger">❌ 搜尋失敗: ${error.message}</div>`;
            }
        }
        
        function showDetails(index) {
            const item = window.searchResults[index];
            if (!item) return;
            
            const data = item.data;
            let details = '<h6>📋 詳細資料</h6><table class="table table-sm">';
            
            for (const [key, value] of Object.entries(data)) {
                if (key !== 'type' && value) {
                    details += `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`;
                }
            }
            
            details += '</table>';
            
            // 使用 Bootstrap Modal 顯示詳情
            const modalHtml = `
                <div class="modal fade" id="detailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">詳細資料 - ${item.type}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">${details}</div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除舊的 modal
            const oldModal = document.getElementById('detailModal');
            if (oldModal) oldModal.remove();
            
            // 添加新的 modal
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // 顯示 modal
            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
        }
        
        // 頁面載入時自動測試
        window.onload = function() {
            log('📱 頁面載入完成');
            log('🔧 準備進行搜尋測試');
        };
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
