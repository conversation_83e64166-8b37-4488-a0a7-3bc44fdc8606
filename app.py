import sys
import logging
logging.basicConfig(stream=sys.stderr, level=logging.DEBUG)


from flask import Flask, render_template, request, redirect, url_for, session
import pandas as pd
from functools import wraps
import os
from datetime import timedelta
import sqlite3
from Sales_information_inquiry.sales_info import sales_bp  # Import the Blueprint
from inventory_system.inventory import inventory_bp  # Import the Inventory Blueprint
from Inventory_inquiry_system.inventory_inquiry import inventory_inquiry_bp  # Import the Inventory Inquiry Blueprint

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 請更改為隨機字串
app.permanent_session_lifetime = timedelta(hours=1)  # 設定 session 過期時間為 1 小時

# 添加 CORS 支援
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# Register the Blueprints
app.register_blueprint(sales_bp)
app.register_blueprint(inventory_bp)
app.register_blueprint(inventory_inquiry_bp)

# 資料庫連接函數
def get_db_connection():
    db_path = os.path.join(os.path.dirname(__file__), 'database', 'id_database.db')
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

# 登入驗證裝飾器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def login():
    if 'logged_in' in session:
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login_post():
    username = request.form.get('username')
    password = request.form.get('password')
    
    try:
        # 連接資料庫
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 查詢使用者
        cursor.execute('SELECT * FROM id_data WHERE username = ? AND password = ?', 
                      (username, password))
        user = cursor.fetchone()
        
        # 關閉資料庫連接
        conn.close()
        
        if user:
            session.permanent = True
            session['logged_in'] = True
            session['username'] = username
            session['name'] = user['name'] if 'name' in user.keys() else ''
            return redirect(url_for('dashboard'))
        else:
            print(f"登入失敗：使用者 {username} 的驗證失敗")
            return render_template('login.html', error='帳號或密碼錯誤')
            
    except sqlite3.Error as e:
        print(f"資料庫錯誤：{str(e)}")
        return render_template('login.html', error='系統設定錯誤，請聯繫管理員')
    except Exception as e:
        print(f"登入過程發生錯誤：{str(e)}")
        return render_template('login.html', error='系統發生錯誤，請稍後再試')

@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

@app.route('/sales')
@login_required
def sales():
    return render_template('sales.html')

@app.route('/assistant')
@login_required
def assistant():
    return render_template('assistant.html')

@app.route('/logout')
def logout():
    session.clear()  # 清除所有 session 資料
    return redirect(url_for('login'))

@app.route('/change_password', methods=['GET'])
def change_password_page():
    return render_template('change_password.html')

@app.route('/change_password', methods=['POST'])
def change_password():
    username = request.form.get('username')
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    
    if new_password != confirm_password:
        return render_template('change_password.html', error='新密碼與確認密碼不符')
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 驗證目前密碼
        cursor.execute('SELECT * FROM id_data WHERE username = ? AND password = ?', 
                      (username, current_password))
        user = cursor.fetchone()
        
        if not user:
            return render_template('change_password.html', error='帳號或目前密碼錯誤')
        
        # 更新密碼
        cursor.execute('UPDATE id_data SET password = ? WHERE username = ?',
                      (new_password, username))
        conn.commit()
        conn.close()
        
        return render_template('change_password.html', success='密碼修改成功')
        
    except sqlite3.Error as e:
        print(f"資料庫錯誤：{str(e)}")
        return render_template('change_password.html', error='系統設定錯誤，請聯繫管理員')
    except Exception as e:
        print(f"密碼修改過程發生錯誤：{str(e)}")
        return render_template('change_password.html', error='系統發生錯誤，請稍後再試')

@app.route('/test_search')
def test_search():
    """搜尋功能測試頁面"""
    return render_template('test_search.html')

@app.route('/input_test')
def input_test():
    """輸入框功能測試頁面"""
    return render_template('input_test.html')

from waitress import serve

if __name__ == '__main__':
    serve(app, host='0.0.0.0', port=8080)




